1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.alwan.kids2025"
4    android:versionCode="3"
5    android:versionName="3" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->Z:\alwan6\app\src\main\AndroidManifest.xml:5:5-67
11-->Z:\alwan6\app\src\main\AndroidManifest.xml:5:22-64
12    <!-- Removed WRITE_EXTERNAL_STORAGE permission -->
13    <!-- Removed permissions related to storage and media -->
14    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
14-->Z:\alwan6\app\src\main\AndroidManifest.xml:8:5-76
14-->Z:\alwan6\app\src\main\AndroidManifest.xml:8:22-74
15    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
15-->Z:\alwan6\app\src\main\AndroidManifest.xml:9:5-79
15-->Z:\alwan6\app\src\main\AndroidManifest.xml:9:22-76
16
17    <!-- Create a unique permission for your app and use it so only your app can receive your OneSignal messages. -->
18    <permission
18-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:7:5-9:47
19        android:name="com.alwan.kids2025.permission.C2D_MESSAGE"
19-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:8:9-63
20        android:protectionLevel="signature" />
20-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:9:9-44
21
22    <uses-permission android:name="com.alwan.kids2025.permission.C2D_MESSAGE" /> <!-- c2dm RECEIVE are basic requirements for push messages through Google's FCM -->
22-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:11:5-79
22-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:11:22-76
23    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" /> <!-- Required, makes sure notifications are delivered on time. -->
23-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:18:5-82
23-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:18:22-79
24    <uses-permission android:name="android.permission.WAKE_LOCK" />
24-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:19:5-68
24-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:19:22-65
25    <!--
26 Required so the device vibrates on receiving a push notification.
27         Vibration settings of the device still apply.
28    -->
29    <uses-permission android:name="android.permission.VIBRATE" />
29-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:24:5-66
29-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:24:22-63
30    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
30-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:25:5-79
30-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:25:22-76
31    <!--
32 Use to restore notifications the user hasn't interacted with.
33         They could be missed notifications if the user reboots their device if this isn't in place.
34    -->
35    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" /> <!-- START: ShortcutBadger -->
35-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:30:5-81
35-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:30:22-78
36    <!-- Samsung -->
37    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
37-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:32:5-86
37-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:32:22-83
38    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- HTC -->
38-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:33:5-87
38-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:33:22-84
39    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
39-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:34:5-81
39-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:34:22-78
40    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- Sony -->
40-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:35:5-83
40-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:35:22-80
41    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
41-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:36:5-88
41-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:36:22-85
42    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- Apex -->
42-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:37:5-92
42-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:37:22-89
43    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- Solid -->
43-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:38:5-84
43-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:38:22-81
44    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- Huawei -->
44-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:39:5-83
44-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:39:22-80
45    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
45-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:40:5-91
45-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:40:22-88
46    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
46-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:41:5-92
46-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:41:22-89
47    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- ZUK -->
47-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:42:5-93
47-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:42:22-90
48    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- OPPO -->
48-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:43:5-73
48-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:43:22-70
49    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
49-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:44:5-82
49-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:44:22-79
50    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- EvMe -->
50-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:45:5-83
50-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:45:22-80
51    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
51-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:46:5-88
51-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:46:22-85
52    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" /> <!-- Android package visibility setting -->
52-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:47:5-89
52-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:47:22-86
53    <queries>
53-->[com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:32:5-48:15
54
55        <!-- For browser content -->
56        <intent>
56-->[com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:35:9-41:18
57            <action android:name="android.intent.action.VIEW" />
57-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:77:17-69
57-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:77:25-66
58
59            <category android:name="android.intent.category.BROWSABLE" />
59-->[com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:38:13-74
59-->[com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:38:23-71
60
61            <data android:scheme="https" />
61-->[com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:40:13-44
61-->[com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:40:19-41
62        </intent>
63        <!-- End of browser content -->
64        <!-- For CustomTabsService -->
65        <intent>
65-->[com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:44:9-46:18
66            <action android:name="android.support.customtabs.action.CustomTabsService" />
66-->[com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:45:13-90
66-->[com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:45:21-87
67        </intent>
68        <!-- End of CustomTabsService -->
69    </queries>
70
71    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
71-->[com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\089fbe965f78042cd7ca720ff60aa6cd\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:26:5-110
71-->[com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\089fbe965f78042cd7ca720ff60aa6cd\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:26:22-107
72    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
72-->[com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd77c4315985c5be26b68c43bc84d5ea\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:26:5-88
72-->[com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd77c4315985c5be26b68c43bc84d5ea\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:26:22-85
73    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
73-->[com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd77c4315985c5be26b68c43bc84d5ea\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:27:5-82
73-->[com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd77c4315985c5be26b68c43bc84d5ea\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:27:22-79
74    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
74-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
74-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:22-74
75
76    <permission
76-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1273d61679a02c43f1de22c3f405d6f\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
77        android:name="com.alwan.kids2025.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
77-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1273d61679a02c43f1de22c3f405d6f\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
78        android:protectionLevel="signature" />
78-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1273d61679a02c43f1de22c3f405d6f\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
79
80    <uses-permission android:name="com.alwan.kids2025.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
80-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1273d61679a02c43f1de22c3f405d6f\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
80-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1273d61679a02c43f1de22c3f405d6f\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
81
82    <application
82-->Z:\alwan6\app\src\main\AndroidManifest.xml:12:5-87:19
83        android:allowBackup="false"
83-->Z:\alwan6\app\src\main\AndroidManifest.xml:13:9-36
84        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
84-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1273d61679a02c43f1de22c3f405d6f\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
85        android:debuggable="true"
86        android:extractNativeLibs="false"
87        android:icon="@drawable/logo"
87-->Z:\alwan6\app\src\main\AndroidManifest.xml:14:9-38
88        android:label="@string/app_name"
88-->Z:\alwan6\app\src\main\AndroidManifest.xml:15:9-41
89        android:largeHeap="true"
89-->Z:\alwan6\app\src\main\AndroidManifest.xml:17:9-33
90        android:requestLegacyExternalStorage="true"
90-->Z:\alwan6\app\src\main\AndroidManifest.xml:19:9-52
91        android:supportsRtl="true"
91-->Z:\alwan6\app\src\main\AndroidManifest.xml:16:9-35
92        android:testOnly="true"
93        android:theme="@style/AppTheme" >
93-->Z:\alwan6\app\src\main\AndroidManifest.xml:18:9-40
94        <meta-data
94-->Z:\alwan6\app\src\main\AndroidManifest.xml:20:9-100
95            android:name="google_analytics_adid_collection_enabled"
95-->Z:\alwan6\app\src\main\AndroidManifest.xml:20:20-75
96            android:value="false" />
96-->Z:\alwan6\app\src\main\AndroidManifest.xml:20:76-97
97        <!-- Removed duplicate OneSignal App ID meta-data entry -->
98
99        <meta-data
99-->Z:\alwan6\app\src\main\AndroidManifest.xml:23:9-25:69
100            android:name="com.google.android.gms.ads.APPLICATION_ID"
100-->Z:\alwan6\app\src\main\AndroidManifest.xml:24:13-69
101            android:value="ca-app-pub-7841751633097845~3195890380" />
101-->Z:\alwan6\app\src\main\AndroidManifest.xml:25:13-67
102
103        <property
103-->Z:\alwan6\app\src\main\AndroidManifest.xml:26:9-30:48
104            android:name="android.adservices.AD_SERVICES_CONFIG"
104-->Z:\alwan6\app\src\main\AndroidManifest.xml:28:13-65
105            android:resource="@xml/gma_ad_services_config" />
105-->Z:\alwan6\app\src\main\AndroidManifest.xml:29:13-59
106
107        <activity android:name="com.alwan.kids2025.MainActivity" />
107-->Z:\alwan6\app\src\main\AndroidManifest.xml:31:9-67
107-->Z:\alwan6\app\src\main\AndroidManifest.xml:31:19-65
108        <activity
108-->Z:\alwan6\app\src\main\AndroidManifest.xml:32:9-41:20
109            android:name="com.alwan.kids2025.Categories"
109-->Z:\alwan6\app\src\main\AndroidManifest.xml:33:13-57
110            android:exported="true"
110-->Z:\alwan6\app\src\main\AndroidManifest.xml:35:13-36
111            android:theme="@style/AppTheme.NoActionBar" >
111-->Z:\alwan6\app\src\main\AndroidManifest.xml:34:13-56
112            <intent-filter>
112-->Z:\alwan6\app\src\main\AndroidManifest.xml:36:13-40:29
113                <action android:name="android.intent.action.MAIN" />
113-->Z:\alwan6\app\src\main\AndroidManifest.xml:37:17-69
113-->Z:\alwan6\app\src\main\AndroidManifest.xml:37:25-66
114
115                <category android:name="android.intent.category.DEFAULT" />
115-->Z:\alwan6\app\src\main\AndroidManifest.xml:39:17-76
115-->Z:\alwan6\app\src\main\AndroidManifest.xml:39:27-73
116            </intent-filter>
117        </activity>
118        <activity
118-->Z:\alwan6\app\src\main\AndroidManifest.xml:42:9-44:59
119            android:name="com.alwan.kids2025.CategoryItems"
119-->Z:\alwan6\app\src\main\AndroidManifest.xml:43:13-60
120            android:theme="@style/AppTheme.NoActionBar" />
120-->Z:\alwan6\app\src\main\AndroidManifest.xml:44:13-56
121        <activity
121-->Z:\alwan6\app\src\main\AndroidManifest.xml:45:9-54:20
122            android:name="com.alwan.kids2025.Splash"
122-->Z:\alwan6\app\src\main\AndroidManifest.xml:47:13-53
123            android:exported="true"
123-->Z:\alwan6\app\src\main\AndroidManifest.xml:46:13-36
124            android:theme="@style/AppTheme.NoActionBar" >
124-->Z:\alwan6\app\src\main\AndroidManifest.xml:48:13-56
125            <intent-filter>
125-->Z:\alwan6\app\src\main\AndroidManifest.xml:49:13-53:29
126                <action android:name="android.intent.action.MAIN" />
126-->Z:\alwan6\app\src\main\AndroidManifest.xml:37:17-69
126-->Z:\alwan6\app\src\main\AndroidManifest.xml:37:25-66
127
128                <category android:name="android.intent.category.LAUNCHER" />
128-->Z:\alwan6\app\src\main\AndroidManifest.xml:52:17-77
128-->Z:\alwan6\app\src\main\AndroidManifest.xml:52:27-74
129            </intent-filter>
130        </activity>
131
132        <!-- New Activities -->
133        <activity
133-->Z:\alwan6\app\src\main\AndroidManifest.xml:57:9-60:74
134            android:name="com.alwan.kids2025.AboutActivity"
134-->Z:\alwan6\app\src\main\AndroidManifest.xml:58:13-60
135            android:parentActivityName="com.alwan.kids2025.Categories"
135-->Z:\alwan6\app\src\main\AndroidManifest.xml:60:13-71
136            android:theme="@style/AppTheme.NoActionBar" />
136-->Z:\alwan6\app\src\main\AndroidManifest.xml:59:13-56
137        <activity
137-->Z:\alwan6\app\src\main\AndroidManifest.xml:61:9-64:74
138            android:name="com.alwan.kids2025.SettingsActivity"
138-->Z:\alwan6\app\src\main\AndroidManifest.xml:62:13-63
139            android:parentActivityName="com.alwan.kids2025.Categories"
139-->Z:\alwan6\app\src\main\AndroidManifest.xml:64:13-71
140            android:theme="@style/AppTheme.NoActionBar" />
140-->Z:\alwan6\app\src\main\AndroidManifest.xml:63:13-56
141        <activity
141-->Z:\alwan6\app\src\main\AndroidManifest.xml:65:9-68:74
142            android:name="com.alwan.kids2025.GalleryActivity"
142-->Z:\alwan6\app\src\main\AndroidManifest.xml:66:13-62
143            android:parentActivityName="com.alwan.kids2025.Categories"
143-->Z:\alwan6\app\src\main\AndroidManifest.xml:68:13-71
144            android:theme="@style/AppTheme.NoActionBar" />
144-->Z:\alwan6\app\src\main\AndroidManifest.xml:67:13-56
145        <activity
145-->Z:\alwan6\app\src\main\AndroidManifest.xml:69:9-72:74
146            android:name="com.alwan.kids2025.FavoritesActivity"
146-->Z:\alwan6\app\src\main\AndroidManifest.xml:70:13-64
147            android:parentActivityName="com.alwan.kids2025.Categories"
147-->Z:\alwan6\app\src\main\AndroidManifest.xml:72:13-71
148            android:theme="@style/AppTheme.NoActionBar" />
148-->Z:\alwan6\app\src\main\AndroidManifest.xml:71:13-56
149        <activity
149-->Z:\alwan6\app\src\main\AndroidManifest.xml:73:9-76:74
150            android:name="com.alwan.kids2025.MoreActivity"
150-->Z:\alwan6\app\src\main\AndroidManifest.xml:74:13-59
151            android:parentActivityName="com.alwan.kids2025.Categories"
151-->Z:\alwan6\app\src\main\AndroidManifest.xml:76:13-71
152            android:theme="@style/AppTheme.NoActionBar" />
152-->Z:\alwan6\app\src\main\AndroidManifest.xml:75:13-56
153
154        <service
154-->Z:\alwan6\app\src\main\AndroidManifest.xml:79:9-86:19
155            android:name="com.alwan.kids2025.MyFirebaseMessagingService"
155-->Z:\alwan6\app\src\main\AndroidManifest.xml:80:13-73
156            android:enabled="true"
156-->Z:\alwan6\app\src\main\AndroidManifest.xml:81:13-35
157            android:exported="true" >
157-->Z:\alwan6\app\src\main\AndroidManifest.xml:82:13-36
158            <intent-filter>
158-->Z:\alwan6\app\src\main\AndroidManifest.xml:83:13-85:29
159                <action android:name="com.google.firebase.MESSAGING_EVENT" />
159-->Z:\alwan6\app\src\main\AndroidManifest.xml:84:17-78
159-->Z:\alwan6\app\src\main\AndroidManifest.xml:84:25-75
160            </intent-filter>
161        </service>
162
163        <receiver
163-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:50:9-61:20
164            android:name="com.onesignal.FCMBroadcastReceiver"
164-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:51:13-62
165            android:exported="true"
165-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:52:13-36
166            android:permission="com.google.android.c2dm.permission.SEND" >
166-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:53:13-73
167
168            <!-- High priority so OneSignal payloads can be filtered from other FCM receivers -->
169            <intent-filter android:priority="999" >
169-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:56:13-60:29
169-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:56:28-50
170                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
170-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:57:17-81
170-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:57:25-78
171
172                <category android:name="com.alwan.kids2025" />
172-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:59:17-61
172-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:59:27-58
173            </intent-filter>
174        </receiver>
175
176        <service
176-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:63:9-69:19
177            android:name="com.onesignal.HmsMessageServiceOneSignal"
177-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:64:13-68
178            android:exported="false" >
178-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:65:13-37
179            <intent-filter>
179-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:66:13-68:29
180                <action android:name="com.huawei.push.action.MESSAGING_EVENT" />
180-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:67:17-81
180-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:67:25-78
181            </intent-filter>
182        </service>
183
184        <activity
184-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:71:9-79:20
185            android:name="com.onesignal.NotificationOpenedActivityHMS"
185-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:72:13-71
186            android:exported="true"
186-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:73:13-36
187            android:noHistory="true"
187-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:74:13-37
188            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
188-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:75:13-72
189            <intent-filter>
189-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:76:13-78:29
190                <action android:name="android.intent.action.VIEW" />
190-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:77:17-69
190-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:77:25-66
191            </intent-filter>
192        </activity>
193
194        <service
194-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:81:9-83:40
195            android:name="com.onesignal.FCMIntentService"
195-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:82:13-58
196            android:exported="false" />
196-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:83:13-37
197        <service
197-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:84:9-87:72
198            android:name="com.onesignal.FCMIntentJobService"
198-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:85:13-61
199            android:exported="false"
199-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:86:13-37
200            android:permission="android.permission.BIND_JOB_SERVICE" />
200-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:87:13-69
201        <service
201-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:88:9-91:43
202            android:name="com.onesignal.SyncService"
202-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:89:13-53
203            android:exported="false"
203-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:90:13-37
204            android:stopWithTask="true" />
204-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:91:13-40
205        <service
205-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:92:9-95:72
206            android:name="com.onesignal.SyncJobService"
206-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:93:13-56
207            android:exported="false"
207-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:94:13-37
208            android:permission="android.permission.BIND_JOB_SERVICE" />
208-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:95:13-69
209
210        <activity
210-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:97:9-100:75
211            android:name="com.onesignal.PermissionsActivity"
211-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:98:13-61
212            android:exported="false"
212-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:99:13-37
213            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
213-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:100:13-72
214
215        <receiver
215-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:102:9-104:39
216            android:name="com.onesignal.NotificationDismissReceiver"
216-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:103:13-69
217            android:exported="true" />
217-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:104:13-36
218        <receiver
218-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:105:9-112:20
219            android:name="com.onesignal.BootUpReceiver"
219-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:106:13-56
220            android:exported="true" >
220-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:107:13-36
221            <intent-filter>
221-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:108:13-111:29
222                <action android:name="android.intent.action.BOOT_COMPLETED" />
222-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:109:17-79
222-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:109:25-76
223                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
223-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:110:17-82
223-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:110:25-79
224            </intent-filter>
225        </receiver>
226        <receiver
226-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:113:9-119:20
227            android:name="com.onesignal.UpgradeReceiver"
227-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:114:13-57
228            android:exported="true" >
228-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:115:13-36
229            <intent-filter>
229-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:116:13-118:29
230                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
230-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:117:17-84
230-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:117:25-81
231            </intent-filter>
232        </receiver>
233
234        <activity
234-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:121:9-127:75
235            android:name="com.onesignal.NotificationOpenedReceiver"
235-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:122:13-68
236            android:excludeFromRecents="true"
236-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:123:13-46
237            android:exported="true"
237-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:124:13-36
238            android:noHistory="true"
238-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:125:13-37
239            android:taskAffinity=""
239-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:126:13-36
240            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
240-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:127:13-72
241        <activity
241-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:128:9-133:75
242            android:name="com.onesignal.NotificationOpenedReceiverAndroid22AndOlder"
242-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:129:13-85
243            android:excludeFromRecents="true"
243-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:130:13-46
244            android:exported="true"
244-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:131:13-36
245            android:noHistory="true"
245-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:132:13-37
246            android:theme="@android:style/Theme.Translucent.NoTitleBar" /> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
246-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:133:13-72
247        <activity
247-->[com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:53:9-58:43
248            android:name="com.google.android.gms.ads.AdActivity"
248-->[com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:54:13-65
249            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
249-->[com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:55:13-122
250            android:exported="false"
250-->[com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:56:13-37
251            android:theme="@android:style/Theme.Translucent" />
251-->[com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:57:13-61
252
253        <provider
253-->[com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:60:9-65:43
254            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
254-->[com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:61:13-76
255            android:authorities="com.alwan.kids2025.mobileadsinitprovider"
255-->[com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:62:13-73
256            android:exported="false"
256-->[com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:63:13-37
257            android:initOrder="100" />
257-->[com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:64:13-36
258
259        <service
259-->[com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:67:9-71:43
260            android:name="com.google.android.gms.ads.AdService"
260-->[com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:68:13-64
261            android:enabled="true"
261-->[com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:69:13-35
262            android:exported="false" />
262-->[com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:70:13-37
263
264        <activity
264-->[com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:73:9-77:43
265            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
265-->[com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:74:13-82
266            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
266-->[com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:75:13-122
267            android:exported="false" />
267-->[com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:76:13-37
268
269        <receiver
269-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:29:9-40:20
270            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
270-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:30:13-78
271            android:exported="true"
271-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:31:13-36
272            android:permission="com.google.android.c2dm.permission.SEND" >
272-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:32:13-73
273            <intent-filter>
273-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:33:13-35:29
274                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
274-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:57:17-81
274-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:57:25-78
275            </intent-filter>
276
277            <meta-data
277-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:37:13-39:40
278                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
278-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:38:17-92
279                android:value="true" />
279-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:39:17-37
280        </receiver>
281        <!--
282             FirebaseMessagingService performs security checks at runtime,
283             but set to not exported to explicitly avoid allowing another app to call it.
284        -->
285        <service
285-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:46:9-53:19
286            android:name="com.google.firebase.messaging.FirebaseMessagingService"
286-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:47:13-82
287            android:directBootAware="true"
287-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:48:13-43
288            android:exported="false" >
288-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:49:13-37
289            <intent-filter android:priority="-500" >
289-->Z:\alwan6\app\src\main\AndroidManifest.xml:83:13-85:29
290                <action android:name="com.google.firebase.MESSAGING_EVENT" />
290-->Z:\alwan6\app\src\main\AndroidManifest.xml:84:17-78
290-->Z:\alwan6\app\src\main\AndroidManifest.xml:84:25-75
291            </intent-filter>
292        </service>
293        <service
293-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:54:9-63:19
294            android:name="com.google.firebase.components.ComponentDiscoveryService"
294-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:55:13-84
295            android:directBootAware="true"
295-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7feb638b6d94dde9b955151585876686\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:32:13-43
296            android:exported="false" >
296-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:56:13-37
297            <meta-data
297-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:57:13-59:85
298                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
298-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:58:17-122
299                android:value="com.google.firebase.components.ComponentRegistrar" />
299-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:59:17-82
300            <meta-data
300-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:60:13-62:85
301                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
301-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:61:17-119
302                android:value="com.google.firebase.components.ComponentRegistrar" />
302-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:62:17-82
303            <meta-data
303-->[com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd77c4315985c5be26b68c43bc84d5ea\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:37:13-39:85
304                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
304-->[com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd77c4315985c5be26b68c43bc84d5ea\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:38:17-139
305                android:value="com.google.firebase.components.ComponentRegistrar" />
305-->[com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd77c4315985c5be26b68c43bc84d5ea\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:39:17-82
306            <meta-data
306-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2640b9fa355971a68270b9af654b19e0\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
307                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
307-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2640b9fa355971a68270b9af654b19e0\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
308                android:value="com.google.firebase.components.ComponentRegistrar" />
308-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2640b9fa355971a68270b9af654b19e0\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
309            <meta-data
309-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2640b9fa355971a68270b9af654b19e0\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
310                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
310-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2640b9fa355971a68270b9af654b19e0\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
311                android:value="com.google.firebase.components.ComponentRegistrar" />
311-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2640b9fa355971a68270b9af654b19e0\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
312            <meta-data
312-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c5f32962fac6ef30638853a7175eef8\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:12:13-14:85
313                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
313-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c5f32962fac6ef30638853a7175eef8\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:13:17-116
314                android:value="com.google.firebase.components.ComponentRegistrar" />
314-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c5f32962fac6ef30638853a7175eef8\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:14:17-82
315            <meta-data
315-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7feb638b6d94dde9b955151585876686\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:35:13-37:85
316                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
316-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7feb638b6d94dde9b955151585876686\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:36:17-109
317                android:value="com.google.firebase.components.ComponentRegistrar" />
317-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7feb638b6d94dde9b955151585876686\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:37:17-82
318            <meta-data
318-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb1fef9846a4ca177767ead91d3f6366\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:27:13-29:85
319                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
319-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb1fef9846a4ca177767ead91d3f6366\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:28:17-115
320                android:value="com.google.firebase.components.ComponentRegistrar" />
320-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb1fef9846a4ca177767ead91d3f6366\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:29:17-82
321        </service>
322
323        <receiver
323-->[com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\089fbe965f78042cd7ca720ff60aa6cd\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:29:9-33:20
324            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
324-->[com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\089fbe965f78042cd7ca720ff60aa6cd\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:30:13-85
325            android:enabled="true"
325-->[com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\089fbe965f78042cd7ca720ff60aa6cd\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:31:13-35
326            android:exported="false" >
326-->[com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\089fbe965f78042cd7ca720ff60aa6cd\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:32:13-37
327        </receiver>
328
329        <service
329-->[com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\089fbe965f78042cd7ca720ff60aa6cd\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:35:9-38:40
330            android:name="com.google.android.gms.measurement.AppMeasurementService"
330-->[com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\089fbe965f78042cd7ca720ff60aa6cd\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:36:13-84
331            android:enabled="true"
331-->[com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\089fbe965f78042cd7ca720ff60aa6cd\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:37:13-35
332            android:exported="false" />
332-->[com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\089fbe965f78042cd7ca720ff60aa6cd\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:38:13-37
333        <service
333-->[com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\089fbe965f78042cd7ca720ff60aa6cd\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:39:9-43:72
334            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
334-->[com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\089fbe965f78042cd7ca720ff60aa6cd\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:40:13-87
335            android:enabled="true"
335-->[com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\089fbe965f78042cd7ca720ff60aa6cd\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:41:13-35
336            android:exported="false"
336-->[com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\089fbe965f78042cd7ca720ff60aa6cd\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:42:13-37
337            android:permission="android.permission.BIND_JOB_SERVICE" />
337-->[com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\089fbe965f78042cd7ca720ff60aa6cd\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:43:13-69
338
339        <activity
339-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a28de868e92df6a446272321a9f2ec8d\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
340            android:name="com.google.android.gms.common.api.GoogleApiActivity"
340-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a28de868e92df6a446272321a9f2ec8d\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:19-85
341            android:exported="false"
341-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a28de868e92df6a446272321a9f2ec8d\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:22:19-43
342            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
342-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a28de868e92df6a446272321a9f2ec8d\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:21:19-78
343
344        <provider
344-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7feb638b6d94dde9b955151585876686\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:23:9-28:39
345            android:name="com.google.firebase.provider.FirebaseInitProvider"
345-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7feb638b6d94dde9b955151585876686\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:24:13-77
346            android:authorities="com.alwan.kids2025.firebaseinitprovider"
346-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7feb638b6d94dde9b955151585876686\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:25:13-72
347            android:directBootAware="true"
347-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7feb638b6d94dde9b955151585876686\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:26:13-43
348            android:exported="false"
348-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7feb638b6d94dde9b955151585876686\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:27:13-37
349            android:initOrder="100" />
349-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7feb638b6d94dde9b955151585876686\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:28:13-36
350
351        <uses-library
351-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0b69a3f59ea6ba71104ab72d23691d1\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
352            android:name="android.ext.adservices"
352-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0b69a3f59ea6ba71104ab72d23691d1\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
353            android:required="false" />
353-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0b69a3f59ea6ba71104ab72d23691d1\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
354        <uses-library
354-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\14c92d843d46f26f2f7adaf6ce888732\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
355            android:name="androidx.window.extensions"
355-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\14c92d843d46f26f2f7adaf6ce888732\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
356            android:required="false" />
356-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\14c92d843d46f26f2f7adaf6ce888732\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
357        <uses-library
357-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\14c92d843d46f26f2f7adaf6ce888732\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
358            android:name="androidx.window.sidecar"
358-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\14c92d843d46f26f2f7adaf6ce888732\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
359            android:required="false" />
359-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\14c92d843d46f26f2f7adaf6ce888732\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
360
361        <provider
361-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e88b5613f2b3b0edb68479dabd57681\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
362            android:name="androidx.startup.InitializationProvider"
362-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e88b5613f2b3b0edb68479dabd57681\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
363            android:authorities="com.alwan.kids2025.androidx-startup"
363-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e88b5613f2b3b0edb68479dabd57681\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
364            android:exported="false" >
364-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e88b5613f2b3b0edb68479dabd57681\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
365            <meta-data
365-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e88b5613f2b3b0edb68479dabd57681\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
366                android:name="androidx.emoji2.text.EmojiCompatInitializer"
366-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e88b5613f2b3b0edb68479dabd57681\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
367                android:value="androidx.startup" />
367-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e88b5613f2b3b0edb68479dabd57681\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
368            <meta-data
368-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
369                android:name="androidx.work.WorkManagerInitializer"
369-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
370                android:value="androidx.startup" />
370-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
371            <meta-data
371-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\755f43156db6b4ab2bd85c5e10c792d9\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
372                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
372-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\755f43156db6b4ab2bd85c5e10c792d9\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
373                android:value="androidx.startup" />
373-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\755f43156db6b4ab2bd85c5e10c792d9\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
374            <meta-data
374-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
375                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
375-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
376                android:value="androidx.startup" />
376-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
377        </provider>
378
379        <service
379-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
380            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
380-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
381            android:directBootAware="false"
381-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
382            android:enabled="@bool/enable_system_alarm_service_default"
382-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
383            android:exported="false" />
383-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
384        <service
384-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
385            android:name="androidx.work.impl.background.systemjob.SystemJobService"
385-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
386            android:directBootAware="false"
386-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
387            android:enabled="@bool/enable_system_job_service_default"
387-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
388            android:exported="true"
388-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
389            android:permission="android.permission.BIND_JOB_SERVICE" />
389-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
390        <service
390-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
391            android:name="androidx.work.impl.foreground.SystemForegroundService"
391-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
392            android:directBootAware="false"
392-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
393            android:enabled="@bool/enable_system_foreground_service_default"
393-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
394            android:exported="false" />
394-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
395
396        <receiver
396-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
397            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
397-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
398            android:directBootAware="false"
398-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
399            android:enabled="true"
399-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
400            android:exported="false" />
400-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
401        <receiver
401-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
402            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
402-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
403            android:directBootAware="false"
403-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
404            android:enabled="false"
404-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
405            android:exported="false" >
405-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
406            <intent-filter>
406-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
407                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
407-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
407-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
408                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
408-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
408-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
409            </intent-filter>
410        </receiver>
411        <receiver
411-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
412            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
412-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
413            android:directBootAware="false"
413-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
414            android:enabled="false"
414-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
415            android:exported="false" >
415-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
416            <intent-filter>
416-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
417                <action android:name="android.intent.action.BATTERY_OKAY" />
417-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
417-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
418                <action android:name="android.intent.action.BATTERY_LOW" />
418-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
418-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
419            </intent-filter>
420        </receiver>
421        <receiver
421-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
422            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
422-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
423            android:directBootAware="false"
423-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
424            android:enabled="false"
424-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
425            android:exported="false" >
425-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
426            <intent-filter>
426-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
427                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
427-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
427-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
428                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
428-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
428-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
429            </intent-filter>
430        </receiver>
431        <receiver
431-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
432            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
432-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
433            android:directBootAware="false"
433-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
434            android:enabled="false"
434-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
435            android:exported="false" >
435-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
436            <intent-filter>
436-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
437                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
437-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
437-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
438            </intent-filter>
439        </receiver>
440        <receiver
440-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
441            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
441-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
442            android:directBootAware="false"
442-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
443            android:enabled="false"
443-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
444            android:exported="false" >
444-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
445            <intent-filter>
445-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
446                <action android:name="android.intent.action.BOOT_COMPLETED" />
446-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:109:17-79
446-->[com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:109:25-76
447                <action android:name="android.intent.action.TIME_SET" />
447-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
447-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
448                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
448-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
448-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
449            </intent-filter>
450        </receiver>
451        <receiver
451-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
452            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
452-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
453            android:directBootAware="false"
453-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
454            android:enabled="@bool/enable_system_alarm_service_default"
454-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
455            android:exported="false" >
455-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
456            <intent-filter>
456-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
457                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
457-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
457-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
458            </intent-filter>
459        </receiver>
460        <receiver
460-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
461            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
461-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
462            android:directBootAware="false"
462-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
463            android:enabled="true"
463-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
464            android:exported="true"
464-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
465            android:permission="android.permission.DUMP" >
465-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
466            <intent-filter>
466-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
467                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
467-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
467-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
468            </intent-filter>
469        </receiver>
470
471        <meta-data
471-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbd131f1b1e0c9680dc8c1d103e011d9\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
472            android:name="com.google.android.gms.version"
472-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbd131f1b1e0c9680dc8c1d103e011d9\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
473            android:value="@integer/google_play_services_version" />
473-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbd131f1b1e0c9680dc8c1d103e011d9\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
474
475        <receiver
475-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
476            android:name="androidx.profileinstaller.ProfileInstallReceiver"
476-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
477            android:directBootAware="false"
477-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
478            android:enabled="true"
478-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
479            android:exported="true"
479-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
480            android:permission="android.permission.DUMP" >
480-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
481            <intent-filter>
481-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
482                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
482-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
482-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
483            </intent-filter>
484            <intent-filter>
484-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
485                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
485-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
485-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
486            </intent-filter>
487            <intent-filter>
487-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
488                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
488-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
488-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
489            </intent-filter>
490            <intent-filter>
490-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
491                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
491-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
491-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
492            </intent-filter>
493        </receiver>
494
495        <service
495-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3bd9b03117c03291bfe953980a6498\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
496            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
496-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3bd9b03117c03291bfe953980a6498\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
497            android:exported="false" >
497-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3bd9b03117c03291bfe953980a6498\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
498            <meta-data
498-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3bd9b03117c03291bfe953980a6498\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
499                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
499-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3bd9b03117c03291bfe953980a6498\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
500                android:value="cct" />
500-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3bd9b03117c03291bfe953980a6498\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
501        </service>
502        <service
502-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54d2a2a8dcd0fe51f6974bf7ef03f497\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
503            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
503-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54d2a2a8dcd0fe51f6974bf7ef03f497\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
504            android:exported="false"
504-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54d2a2a8dcd0fe51f6974bf7ef03f497\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
505            android:permission="android.permission.BIND_JOB_SERVICE" >
505-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54d2a2a8dcd0fe51f6974bf7ef03f497\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
506        </service>
507
508        <receiver
508-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54d2a2a8dcd0fe51f6974bf7ef03f497\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
509            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
509-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54d2a2a8dcd0fe51f6974bf7ef03f497\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
510            android:exported="false" />
510-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54d2a2a8dcd0fe51f6974bf7ef03f497\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
511
512        <service
512-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9169a328e04980931295dda3f79d951\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
513            android:name="androidx.room.MultiInstanceInvalidationService"
513-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9169a328e04980931295dda3f79d951\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
514            android:directBootAware="true"
514-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9169a328e04980931295dda3f79d951\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
515            android:exported="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
515-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9169a328e04980931295dda3f79d951\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
516        <activity
516-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06c860c5adf21788ed095535b14fdcc0\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:14:9-18:65
517            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
517-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06c860c5adf21788ed095535b14fdcc0\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:15:13-93
518            android:exported="false"
518-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06c860c5adf21788ed095535b14fdcc0\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:16:13-37
519            android:stateNotNeeded="true"
519-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06c860c5adf21788ed095535b14fdcc0\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:17:13-42
520            android:theme="@style/Theme.PlayCore.Transparent" />
520-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06c860c5adf21788ed095535b14fdcc0\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:18:13-62
521    </application>
522
523</manifest>
