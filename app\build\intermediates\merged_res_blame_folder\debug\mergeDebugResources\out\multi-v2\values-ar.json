{"logs": [{"outputFile": "com.alwan.kids2025.app-mergeDebugResources-56:/values-ar/values-ar.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\952446e2a2fa218bc85ba4e6563abcfd\\transformed\\jetified-play-services-ads-22.0.0\\res\\values-ar\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,240,288,342,410,510,563,677,734,846,931,969,1048,1080,1111,1154,1222,1262", "endColumns": "40,47,53,67,99,52,113,56,111,84,37,78,31,30,42,67,39,55", "endOffsets": "239,287,341,409,509,562,676,733,845,930,968,1047,1079,1110,1153,1221,1261,1317"}, "to": {"startLines": "219,220,221,240,241,242,243,244,245,246,259,260,261,262,263,264,265,293", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16647,16692,16744,17845,17917,18021,18078,18196,18257,18373,19184,19226,19309,19345,19380,19427,19499,21350", "endColumns": "44,51,57,71,103,56,117,60,115,88,41,82,35,34,46,71,43,59", "endOffsets": "16687,16739,16797,17912,18016,18073,18191,18252,18368,18457,19221,19304,19340,19375,19422,19494,19538,21405"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f1a8cebdf235588805d6f74854d945d7\\transformed\\preference-1.2.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,172,265,351,484,653,735", "endColumns": "66,92,85,132,168,81,79", "endOffsets": "167,260,346,479,648,730,810"}, "to": {"startLines": "114,133,235,250,281,291,292", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "8806,10214,17479,18628,20548,21188,21270", "endColumns": "66,92,85,132,168,81,79", "endOffsets": "8868,10302,17560,18756,20712,21265,21345"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4630b1237475cc69c83d8707fc0d9479\\transformed\\browser-1.4.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,361", "endColumns": "99,97,107,101", "endOffsets": "150,248,356,458"}, "to": {"startLines": "115,137,138,139", "startColumns": "4,4,4,4", "startOffsets": "8873,10513,10611,10719", "endColumns": "99,97,107,101", "endOffsets": "8968,10606,10714,10816"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cbd131f1b1e0c9680dc8c1d103e011d9\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-ar\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "104", "startColumns": "4", "startOffsets": "7664", "endColumns": "129", "endOffsets": "7789"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\36dc858f18a2a1e2b92bb4036b7b9aba\\transformed\\material-1.12.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,465,543,619,703,795,878,979,1098,1175,1234,1297,1388,1457,1524,1624,1687,1752,1813,1881,1943,2001,2115,2175,2236,2293,2366,2489,2570,2662,2769,2867,2947,3095,3176,3257,3385,3474,3550,3603,3657,3723,3801,3881,3952,4034,4106,4180,4253,4323,4432,4523,4594,4684,4779,4853,4936,5029,5078,5159,5228,5314,5399,5461,5525,5588,5657,5766,5876,5973,6073,6130,6188,6268,6347,6422", "endLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84", "endColumns": "12,77,75,83,91,82,100,118,76,58,62,90,68,66,99,62,64,60,67,61,57,113,59,60,56,72,122,80,91,106,97,79,147,80,80,127,88,75,52,53,65,77,79,70,81,71,73,72,69,108,90,70,89,94,73,82,92,48,80,68,85,84,61,63,62,68,108,109,96,99,56,57,79,78,74,75", "endOffsets": "460,538,614,698,790,873,974,1093,1170,1229,1292,1383,1452,1519,1619,1682,1747,1808,1876,1938,1996,2110,2170,2231,2288,2361,2484,2565,2657,2764,2862,2942,3090,3171,3252,3380,3469,3545,3598,3652,3718,3796,3876,3947,4029,4101,4175,4248,4318,4427,4518,4589,4679,4774,4848,4931,5024,5073,5154,5223,5309,5394,5456,5520,5583,5652,5761,5871,5968,6068,6125,6183,6263,6342,6417,6493"}, "to": {"startLines": "2,73,74,75,76,77,89,90,95,120,121,136,150,152,159,160,161,162,163,164,165,166,167,168,169,170,171,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,248,269,270,273", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,4874,4952,5028,5112,5204,6191,6292,6633,9208,9267,10422,11336,11459,11889,11989,12052,12117,12178,12246,12308,12366,12480,12540,12601,12658,12731,12894,12975,13067,13174,13272,13352,13500,13581,13662,13790,13879,13955,14008,14062,14128,14206,14286,14357,14439,14511,14585,14658,14728,14837,14928,14999,15089,15184,15258,15341,15434,15483,15564,15633,15719,15804,15866,15930,15993,16062,16171,16281,16378,16478,16535,18499,19747,19826,19999", "endLines": "9,73,74,75,76,77,89,90,95,120,121,136,150,152,159,160,161,162,163,164,165,166,167,168,169,170,171,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,248,269,270,273", "endColumns": "12,77,75,83,91,82,100,118,76,58,62,90,68,66,99,62,64,60,67,61,57,113,59,60,56,72,122,80,91,106,97,79,147,80,80,127,88,75,52,53,65,77,79,70,81,71,73,72,69,108,90,70,89,94,73,82,92,48,80,68,85,84,61,63,62,68,108,109,96,99,56,57,79,78,74,75", "endOffsets": "510,4947,5023,5107,5199,5282,6287,6406,6705,9262,9325,10508,11400,11521,11984,12047,12112,12173,12241,12303,12361,12475,12535,12596,12653,12726,12849,12970,13062,13169,13267,13347,13495,13576,13657,13785,13874,13950,14003,14057,14123,14201,14281,14352,14434,14506,14580,14653,14723,14832,14923,14994,15084,15179,15253,15336,15429,15478,15559,15628,15714,15799,15861,15925,15988,16057,16166,16276,16373,16473,16530,16588,18574,19821,19896,20070"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d1273d61679a02c43f1de22c3f405d6f\\transformed\\core-1.13.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,250,345,448,551,653,767", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "143,245,340,443,546,648,762,863"}, "to": {"startLines": "79,80,81,82,83,84,85,280", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "5339,5432,5534,5629,5732,5835,5937,20447", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "5427,5529,5624,5727,5830,5932,6046,20543"}}, {"source": "Z:\\alwan6\\app\\src\\main\\res\\values-ar\\strings.xml", "from": {"startLines": "6,10,14,47,44,3,2,50,126,35,111,99,131,71,1,72,63,141,140,62,139,138,73,13,110,98,52,105,148,149,65,145,144,38,154,152,153,45,43,41,40,42,78,76,77,127,121,124,109,97,112,100,117,36,39,143,142,67,94,135,93,66,46,9,130,118,114,102,82,5,4,84,83,81,85,37,125,119,51,55,54,57,56,12,8,69,68,33,88,90,89,11,70,7,120,60,64,34,61,137,136,49,48,132,158,162,160,159,161,157,113,101,106", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "285,482,641,2243,1986,117,68,2405,5792,1323,5158,4652,6004,3507,17,3561,3042,6608,6537,2979,6456,6376,3616,599,5112,4597,2508,4896,6966,7022,3148,6866,6797,1493,7222,7109,7167,2051,1914,1722,1596,1831,3869,3697,3757,5875,5606,5689,5068,4544,5205,4708,5367,1370,1541,6738,6680,3261,4453,6154,4396,3200,2155,439,5963,5411,5299,4815,3989,224,162,4088,4040,3942,4139,1441,5736,5466,2452,2621,2562,2765,2683,561,389,3379,3316,1218,4216,4315,4264,521,3438,341,5530,2871,3096,1271,2919,6298,6221,2349,2294,6060,7380,7583,7483,7433,7536,7326,5248,4759,4958", "endLines": "6,10,28,47,44,3,2,50,126,35,111,99,131,71,1,72,63,141,140,62,139,138,73,13,110,98,52,105,148,149,65,145,144,38,154,152,153,45,43,41,40,42,78,76,77,127,121,124,109,97,112,100,117,36,39,143,142,67,94,135,93,66,46,9,130,118,114,102,82,5,4,84,83,81,85,37,125,119,51,55,54,57,56,12,8,69,68,33,88,90,89,11,70,7,120,60,64,34,61,137,136,49,48,132,158,162,160,159,161,157,113,101,106", "endColumns": "54,37,59,49,63,43,47,45,81,45,45,54,54,52,49,53,52,70,69,61,79,78,51,40,44,53,50,60,54,54,50,68,67,46,68,56,53,102,70,107,124,81,44,58,110,58,55,45,42,51,41,49,42,69,53,57,56,53,51,65,55,59,86,41,39,53,42,51,49,59,60,49,46,45,43,50,54,62,54,60,57,79,80,36,48,57,61,51,46,46,49,38,67,46,74,46,50,50,58,76,75,54,53,61,51,51,51,48,45,52,49,54,77", "endOffsets": "335,515,1154,2288,2045,156,111,2446,5869,1364,5199,4702,6054,3555,62,3610,3090,6674,6602,3036,6531,6450,3663,635,5152,4646,2554,4952,7016,7072,3194,6930,6860,1535,7286,7161,7216,2149,1980,1825,1716,1908,3909,3751,3863,5929,5657,5730,5106,4591,5242,4753,5405,1435,1590,6791,6732,3310,4500,6215,4447,3255,2237,476,5998,5460,5337,4862,4034,279,218,4133,4082,3983,4178,1487,5786,5524,2502,2677,2615,2840,2759,593,433,3432,3373,1265,4258,4357,4309,555,3501,383,5600,2913,3142,1317,2973,6370,6292,2399,2343,6117,7427,7630,7530,7477,7577,7374,5293,4809,5031"}, "to": {"startLines": "10,38,39,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,78,86,87,88,91,92,93,94,116,117,118,119,122,123,124,125,126,127,128,129,130,131,132,134,135,140,141,142,143,144,145,146,147,148,149,151,153,154,155,156,157,158,172,218,222,223,224,225,226,227,228,229,230,231,232,233,234,236,237,238,239,247,249,251,252,253,254,255,256,257,258,266,267,271,272,274,275,276,277,278,279,282,283,284,285,286,287,288,289,290,294", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "515,3224,3262,3766,3816,3880,3924,3972,4018,4100,4146,4192,4247,4302,4355,4405,4459,4512,4583,4653,4715,4795,5287,6051,6092,6137,6411,6462,6523,6578,8973,9024,9093,9161,9330,9399,9456,9510,9613,9684,9792,9917,9999,10044,10103,10307,10366,10821,10867,10910,10962,11004,11054,11097,11167,11221,11279,11405,11526,11578,11644,11700,11760,11847,12854,16593,16802,16845,16897,16947,17007,17068,17118,17165,17211,17255,17306,17361,17424,17565,17626,17684,17764,18462,18579,18761,18819,18881,18933,18980,19027,19077,19116,19543,19590,19901,19948,20075,20126,20185,20262,20338,20393,20717,20779,20831,20883,20935,20984,21030,21083,21133,21410", "endLines": "10,38,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,78,86,87,88,91,92,93,94,116,117,118,119,122,123,124,125,126,127,128,129,130,131,132,134,135,140,141,142,143,144,145,146,147,148,149,151,153,154,155,156,157,158,172,218,222,223,224,225,226,227,228,229,230,231,232,233,234,236,237,238,239,247,249,251,252,253,254,255,256,257,258,266,267,271,272,274,275,276,277,278,279,282,283,284,285,286,287,288,289,290,294", "endColumns": "54,37,59,49,63,43,47,45,81,45,45,54,54,52,49,53,52,70,69,61,79,78,51,40,44,53,50,60,54,54,50,68,67,46,68,56,53,102,70,107,124,81,44,58,110,58,55,45,42,51,41,49,42,69,53,57,56,53,51,65,55,59,86,41,39,53,42,51,49,59,60,49,46,45,43,50,54,62,54,60,57,79,80,36,48,57,61,51,46,46,49,38,67,46,74,46,50,50,58,76,75,54,53,61,51,51,51,48,45,52,49,54,77", "endOffsets": "565,3257,3761,3811,3875,3919,3967,4013,4095,4141,4187,4242,4297,4350,4400,4454,4507,4578,4648,4710,4790,4869,5334,6087,6132,6186,6457,6518,6573,6628,9019,9088,9156,9203,9394,9451,9505,9608,9679,9787,9912,9994,10039,10098,10209,10361,10417,10862,10905,10957,10999,11049,11092,11162,11216,11274,11331,11454,11573,11639,11695,11755,11842,11884,12889,16642,16840,16892,16942,17002,17063,17113,17160,17206,17250,17301,17356,17419,17474,17621,17679,17759,17840,18494,18623,18814,18876,18928,18975,19022,19072,19111,19179,19585,19660,19943,19994,20121,20180,20257,20333,20388,20442,20774,20826,20878,20930,20979,21025,21078,21128,21183,21483"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\40acce1c3a2b617941018f107a306df6\\transformed\\appcompat-1.7.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,424,506,607,721,801,880,971,1064,1156,1250,1350,1443,1538,1631,1722,1816,1895,2000,2098,2196,2304,2404,2507,2662,2759", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "208,312,419,501,602,716,796,875,966,1059,1151,1245,1345,1438,1533,1626,1717,1811,1890,1995,2093,2191,2299,2399,2502,2657,2754,2836"}, "to": {"startLines": "11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,268", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "570,678,782,889,971,1072,1186,1266,1345,1436,1529,1621,1715,1815,1908,2003,2096,2187,2281,2360,2465,2563,2661,2769,2869,2972,3127,19665", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "673,777,884,966,1067,1181,1261,1340,1431,1524,1616,1710,1810,1903,1998,2091,2182,2276,2355,2460,2558,2656,2764,2864,2967,3122,3219,19742"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a28de868e92df6a446272321a9f2ec8d\\transformed\\jetified-play-services-base-18.0.1\\res\\values-ar\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,433,551,652,786,910,1017,1115,1248,1348,1494,1612,1747,1889,1949,2011", "endColumns": "99,139,117,100,133,123,106,97,132,99,145,117,134,141,59,61,79", "endOffsets": "292,432,550,651,785,909,1016,1114,1247,1347,1493,1611,1746,1888,1948,2010,2090"}, "to": {"startLines": "96,97,98,99,100,101,102,103,105,106,107,108,109,110,111,112,113", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6710,6814,6958,7080,7185,7323,7451,7562,7794,7931,8035,8185,8307,8446,8592,8656,8722", "endColumns": "103,143,121,104,137,127,110,101,136,103,149,121,138,145,63,65,83", "endOffsets": "6809,6953,7075,7180,7318,7446,7557,7659,7926,8030,8180,8302,8441,8587,8651,8717,8801"}}]}]}