{"logs": [{"outputFile": "com.alwan.kids2025.app-mergeDebugResources-56:/values-ar/values-ar.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\952446e2a2fa218bc85ba4e6563abcfd\\transformed\\jetified-play-services-ads-22.0.0\\res\\values-ar\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,240,288,342,410,510,563,677,734,846,931,969,1048,1080,1111,1154,1222,1262", "endColumns": "40,47,53,67,99,52,113,56,111,84,37,78,31,30,42,67,39,55", "endOffsets": "239,287,341,409,509,562,676,733,845,930,968,1047,1079,1110,1153,1221,1261,1317"}, "to": {"startLines": "179,180,181,192,193,194,195,196,197,198,205,206,207,208,209,210,211,226", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14260,14305,14357,15049,15121,15225,15282,15400,15461,15577,16056,16098,16181,16217,16252,16299,16371,17455", "endColumns": "44,51,57,71,103,56,117,60,115,88,41,82,35,34,46,71,43,59", "endOffsets": "14300,14352,14410,15116,15220,15277,15395,15456,15572,15661,16093,16176,16212,16247,16294,16366,16410,17510"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f1a8cebdf235588805d6f74854d945d7\\transformed\\preference-1.2.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,172,265,351,484,653,735", "endColumns": "66,92,85,132,168,81,79", "endOffsets": "167,260,346,479,648,730,810"}, "to": {"startLines": "98,108,187,202,222,224,225", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7811,8636,14683,15832,17082,17293,17375", "endColumns": "66,92,85,132,168,81,79", "endOffsets": "7873,8724,14764,15960,17246,17370,17450"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4630b1237475cc69c83d8707fc0d9479\\transformed\\browser-1.4.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,361", "endColumns": "99,97,107,101", "endOffsets": "150,248,356,458"}, "to": {"startLines": "99,110,111,112", "startColumns": "4,4,4,4", "startOffsets": "7878,8820,8918,9026", "endColumns": "99,97,107,101", "endOffsets": "7973,8913,9021,9123"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cbd131f1b1e0c9680dc8c1d103e011d9\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-ar\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "88", "startColumns": "4", "startOffsets": "6669", "endColumns": "129", "endOffsets": "6794"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\36dc858f18a2a1e2b92bb4036b7b9aba\\transformed\\material-1.12.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,465,543,619,703,795,878,979,1098,1175,1234,1297,1388,1457,1524,1624,1687,1752,1813,1881,1943,2001,2115,2175,2236,2293,2366,2489,2570,2662,2769,2867,2947,3095,3176,3257,3385,3474,3550,3603,3657,3723,3801,3881,3952,4034,4106,4180,4253,4323,4432,4523,4594,4684,4779,4853,4936,5029,5078,5159,5228,5314,5399,5461,5525,5588,5657,5766,5876,5973,6073,6130,6188,6268,6347,6422", "endLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84", "endColumns": "12,77,75,83,91,82,100,118,76,58,62,90,68,66,99,62,64,60,67,61,57,113,59,60,56,72,122,80,91,106,97,79,147,80,80,127,88,75,52,53,65,77,79,70,81,71,73,72,69,108,90,70,89,94,73,82,92,48,80,68,85,84,61,63,62,68,108,109,96,99,56,57,79,78,74,75", "endOffsets": "460,538,614,698,790,873,974,1093,1170,1229,1292,1383,1452,1519,1619,1682,1747,1808,1876,1938,1996,2110,2170,2231,2288,2361,2484,2565,2657,2764,2862,2942,3090,3171,3252,3380,3469,3545,3598,3652,3718,3796,3876,3947,4029,4101,4175,4248,4318,4427,4518,4589,4679,4774,4848,4931,5024,5073,5154,5223,5309,5394,5456,5520,5583,5652,5761,5871,5968,6068,6125,6183,6263,6342,6417,6493"}, "to": {"startLines": "2,62,63,64,65,66,76,77,79,101,102,109,117,118,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,200,214,215,217", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,4158,4236,4312,4396,4488,5367,5468,5638,8025,8084,8729,9331,9400,9596,9696,9759,9824,9885,9953,10015,10073,10187,10247,10308,10365,10438,10561,10642,10734,10841,10939,11019,11167,11248,11329,11457,11546,11622,11675,11729,11795,11873,11953,12024,12106,12178,12252,12325,12395,12504,12595,12666,12756,12851,12925,13008,13101,13150,13231,13300,13386,13471,13533,13597,13660,13729,13838,13948,14045,14145,14202,15703,16544,16623,16745", "endLines": "9,62,63,64,65,66,76,77,79,101,102,109,117,118,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,200,214,215,217", "endColumns": "12,77,75,83,91,82,100,118,76,58,62,90,68,66,99,62,64,60,67,61,57,113,59,60,56,72,122,80,91,106,97,79,147,80,80,127,88,75,52,53,65,77,79,70,81,71,73,72,69,108,90,70,89,94,73,82,92,48,80,68,85,84,61,63,62,68,108,109,96,99,56,57,79,78,74,75", "endOffsets": "510,4231,4307,4391,4483,4566,5463,5582,5710,8079,8142,8815,9395,9462,9691,9754,9819,9880,9948,10010,10068,10182,10242,10303,10360,10433,10556,10637,10729,10836,10934,11014,11162,11243,11324,11452,11541,11617,11670,11724,11790,11868,11948,12019,12101,12173,12247,12320,12390,12499,12590,12661,12751,12846,12920,13003,13096,13145,13226,13295,13381,13466,13528,13592,13655,13724,13833,13943,14040,14140,14197,14255,15778,16618,16693,16816"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d1273d61679a02c43f1de22c3f405d6f\\transformed\\core-1.13.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,250,345,448,551,653,767", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "143,245,340,443,546,648,762,863"}, "to": {"startLines": "67,68,69,70,71,72,73,221", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4571,4664,4766,4861,4964,5067,5169,16981", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "4659,4761,4856,4959,5062,5164,5278,17077"}}, {"source": "Z:\\alwan6\\app\\src\\main\\res\\values-ar\\strings.xml", "from": {"startLines": "6,10,14,54,51,3,2,57,42,34,1,13,32,59,45,52,50,48,47,49,31,33,43,46,53,9,35,5,4,44,58,62,61,64,63,12,8,39,11,7,41,40,56,55,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "285,482,641,2569,2312,117,68,2731,1649,1312,17,599,1229,2834,1819,2377,2240,2048,1922,2157,1187,1273,1696,1867,2481,439,1357,224,162,1767,2778,2947,2888,3091,3009,561,389,1496,521,341,1601,1549,2675,2620,1399", "endLines": "6,10,28,54,51,3,2,57,42,34,1,13,32,59,45,52,50,48,47,49,31,33,43,46,53,9,35,5,4,44,58,62,61,64,63,12,8,39,11,7,41,40,56,55,36", "endColumns": "54,37,59,49,63,43,47,45,45,43,49,40,42,50,46,102,70,107,124,81,40,37,69,53,86,41,40,59,60,50,54,60,57,79,80,36,48,51,38,46,46,50,54,53,41", "endOffsets": "335,515,1154,2614,2371,156,111,2772,1690,1351,62,635,1267,2880,1861,2475,2306,2151,2042,2234,1223,1306,1761,1916,2563,476,1393,279,218,1813,2828,3003,2941,3166,3085,593,433,1543,555,383,1643,1595,2725,2669,1436"}, "to": {"startLines": "10,38,39,54,55,56,57,58,59,60,61,74,75,78,100,103,104,105,106,107,113,114,115,116,119,120,182,183,184,185,186,188,189,190,191,199,201,203,204,212,216,218,219,220,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "515,3224,3262,3766,3816,3880,3924,3972,4018,4064,4108,5283,5324,5587,7978,8147,8250,8321,8429,8554,9128,9169,9207,9277,9467,9554,14415,14456,14516,14577,14628,14769,14830,14888,14968,15666,15783,15965,16017,16415,16698,16821,16872,16927,17251", "endLines": "10,38,53,54,55,56,57,58,59,60,61,74,75,78,100,103,104,105,106,107,113,114,115,116,119,120,182,183,184,185,186,188,189,190,191,199,201,203,204,212,216,218,219,220,223", "endColumns": "54,37,59,49,63,43,47,45,45,43,49,40,42,50,46,102,70,107,124,81,40,37,69,53,86,41,40,59,60,50,54,60,57,79,80,36,48,51,38,46,46,50,54,53,41", "endOffsets": "565,3257,3761,3811,3875,3919,3967,4013,4059,4103,4153,5319,5362,5633,8020,8245,8316,8424,8549,8631,9164,9202,9272,9326,9549,9591,14451,14511,14572,14623,14678,14825,14883,14963,15044,15698,15827,16012,16051,16457,16740,16867,16922,16976,17288"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\40acce1c3a2b617941018f107a306df6\\transformed\\appcompat-1.7.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,424,506,607,721,801,880,971,1064,1156,1250,1350,1443,1538,1631,1722,1816,1895,2000,2098,2196,2304,2404,2507,2662,2759", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "208,312,419,501,602,716,796,875,966,1059,1151,1245,1345,1438,1533,1626,1717,1811,1890,1995,2093,2191,2299,2399,2502,2657,2754,2836"}, "to": {"startLines": "11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,213", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "570,678,782,889,971,1072,1186,1266,1345,1436,1529,1621,1715,1815,1908,2003,2096,2187,2281,2360,2465,2563,2661,2769,2869,2972,3127,16462", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "673,777,884,966,1067,1181,1261,1340,1431,1524,1616,1710,1810,1903,1998,2091,2182,2276,2355,2460,2558,2656,2764,2864,2967,3122,3219,16539"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a28de868e92df6a446272321a9f2ec8d\\transformed\\jetified-play-services-base-18.0.1\\res\\values-ar\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,433,551,652,786,910,1017,1115,1248,1348,1494,1612,1747,1889,1949,2011", "endColumns": "99,139,117,100,133,123,106,97,132,99,145,117,134,141,59,61,79", "endOffsets": "292,432,550,651,785,909,1016,1114,1247,1347,1493,1611,1746,1888,1948,2010,2090"}, "to": {"startLines": "80,81,82,83,84,85,86,87,89,90,91,92,93,94,95,96,97", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5715,5819,5963,6085,6190,6328,6456,6567,6799,6936,7040,7190,7312,7451,7597,7661,7727", "endColumns": "103,143,121,104,137,127,110,101,136,103,149,121,138,145,63,65,83", "endOffsets": "5814,5958,6080,6185,6323,6451,6562,6664,6931,7035,7185,7307,7446,7592,7656,7722,7806"}}]}]}