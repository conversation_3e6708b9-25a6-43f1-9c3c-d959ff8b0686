package com.alwan.kids2025;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import androidx.annotation.LayoutRes;
import androidx.activity.OnBackPressedCallback;
import com.google.android.ump.ConsentInformation;
import com.google.android.ump.UserMessagingPlatform;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import android.view.MenuItem;
import android.view.View;
import android.widget.FrameLayout;

public class BaseActivity extends AppCompatActivity {
    int code;
    private Toolbar toolbar;

    @Override
    public void setContentView(@LayoutRes int layoutResID) {
        // Load the simple base layout without drawer
        super.setContentView(R.layout.activity_base_no_drawer);

        FrameLayout activityContainer = findViewById(R.id.activity_content);
        getLayoutInflater().inflate(layoutResID, activityContainer, true);

        toolbar = findViewById(R.id.toolbar);

        if (useToolbar()) {
            setSupportActionBar(toolbar);
        } else {
            toolbar.setVisibility(View.GONE);
        }

        setUpNavView();
    }

    protected boolean useToolbar() {
        return true;
    }

    protected void setUpNavView() {
        // Simple navigation setup without drawer
        if (useToolbar() && getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();
        switch (id) {
            case R.id.mainmenu:
                code = 1;
                Intent i = new Intent(this, Categories.class);
                i.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                i.putExtra("code", code);
                startActivity(i);
                return true;
            case R.id.categories:
                Intent categoriesIntent = new Intent(this, Categories.class);
                startActivity(categoriesIntent);
                return true;
            case R.id.gallery:
                Intent galleryIntent = new Intent(this, GalleryActivity.class);
                startActivity(galleryIntent);
                return true;
            case R.id.favorites:
                Intent favoritesIntent = new Intent(this, FavoritesActivity.class);
                startActivity(favoritesIntent);
                return true;
            case R.id.settings:
                Intent settingsIntent = new Intent(this, SettingsActivity.class);
                startActivity(settingsIntent);
                return true;
            case R.id.about:
                Intent aboutIntent = new Intent(this, AboutActivity.class);
                startActivity(aboutIntent);
                return true;
            case R.id.share:
                shareTextUrl();
                return true;
            case R.id.rate:
                String package_name = getPackageName();
                Intent r = new Intent(Intent.ACTION_VIEW, Uri.parse("https://play.google.com/store/apps/details?id=" + package_name));
                startActivity(r);
                return true;
            case R.id.support:
                // Open support email
                Intent emailIntent = new Intent(Intent.ACTION_SENDTO);
                emailIntent.setData(Uri.parse("mailto:<EMAIL>"));
                emailIntent.putExtra(Intent.EXTRA_SUBJECT, "دعم تطبيق التلوين - Color Trip");
                emailIntent.putExtra(Intent.EXTRA_TEXT, "مرحباً،\n\nأحتاج مساعدة في:\n\n");
                try {
                    startActivity(Intent.createChooser(emailIntent, "إرسال رسالة دعم"));
                } catch (Exception e) {
                    // Fallback to privacy policy if no email app is available
                    Intent fallbackIntent = new Intent(Intent.ACTION_VIEW, Uri.parse("https://sites.google.com/view/colors-kids"));
                    startActivity(fallbackIntent);
                }
                return true;
            case R.id.ads:
                adsSettings();
                return true;
            case R.id.privacyPolicy:
                Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse("https://sites.google.com/view/colors-kids"));
                startActivity(intent);
                return true;
        }
        return super.onOptionsItemSelected(item);
    }

    private void shareTextUrl() {
        String package_name = getPackageName();
        Intent share = new Intent(Intent.ACTION_SEND);
        share.setType("text/plain");
        share.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        share.putExtra(Intent.EXTRA_TEXT, "Let your child express their imagination and draw with beautiful colors in a fun and safe way!\nDownload the app now and let them enjoy hours of creativity and coloring 🎉💛\nhttps://play.google.com/store/apps/details?id=" + package_name);
        startActivity(Intent.createChooser(share, getString(R.string.Share_The_App)));
    }

    public void adsSettings() {
        ConsentInformation consentInformation = UserMessagingPlatform.getConsentInformation(this);
        consentInformation.reset();
        Intent i4 = new Intent(this, Splash.class);
        i4.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(i4);
    }

    @Override
    public void onBackPressed() {
        if (shouldShowExitDialog()) {
            showExitConfirmationDialog();
        } else {
            goBackToMainActivity();
            super.onBackPressed();
        }
    }

    protected boolean shouldShowExitDialog() {
        // Show exit dialog only for main Categories activity
        String className = getClass().getSimpleName();
        return className.equals("Categories");
    }

    private void goBackToMainActivity() {
        String className = getClass().getSimpleName();
        if (!className.equals("Categories")) {
            // Go back to main Categories activity
            finish();
        }
    }

    private void showExitConfirmationDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("Exit Confirmation");
        builder.setMessage("Are you sure you want to exit the app?\nYour progress will be saved automatically.");
        builder.setPositiveButton("Exit", (dialog, which) -> {
            finishAffinity(); // Close all activities and exit app
        });
        builder.setNegativeButton("Cancel", (dialog, which) -> {
            dialog.dismiss();
        });
        builder.show();
    }
}
