package com.alwan.kids2025;

import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.app.Activity;
import android.content.Intent;
import android.graphics.Typeface;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.cardview.widget.CardView;
import androidx.core.content.ContextCompat;

public class BottomNavigationHelper {

    // Tab indices
    public static final int TAB_HOME = 0;
    public static final int TAB_CATEGORIES = 1;
    public static final int TAB_GALLERY = 2;
    public static final int TAB_FAVORITES = 3;
    public static final int TAB_MORE = 4;

    public static void setupBottomNavigation(Activity activity, int activeTabIndex) {
        LinearLayout navHome = activity.findViewById(R.id.nav_home);
        LinearLayout navCategories = activity.findViewById(R.id.nav_categories);
        LinearLayout navGallery = activity.findViewById(R.id.nav_gallery);
        LinearLayout navFavorites = activity.findViewById(R.id.nav_favorites);
        LinearLayout navMore = activity.findViewById(R.id.nav_more);

        // Reset all tabs first
        resetAllTabs(activity);

        // Set active tab
        setActiveTab(activity, activeTabIndex);

        // Setup click listeners
        setupClickListeners(activity, navHome, navCategories, navGallery, navFavorites, navMore);
    }

    private static void setupClickListeners(Activity activity, LinearLayout navHome, 
                                          LinearLayout navCategories, LinearLayout navGallery, 
                                          LinearLayout navFavorites, LinearLayout navMore) {
        
        if (navHome != null) {
            navHome.setOnClickListener(v -> {
                animateTabClick(v);
                if (!activity.getClass().getSimpleName().equals("Categories")) {
                    Intent intent = new Intent(activity, Categories.class);
                    intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                    activity.startActivity(intent);
                    activity.finish();
                }
            });
        }

        if (navCategories != null) {
            navCategories.setOnClickListener(v -> {
                animateTabClick(v);
                if (!activity.getClass().getSimpleName().equals("Categories")) {
                    Intent intent = new Intent(activity, Categories.class);
                    activity.startActivity(intent);
                    activity.finish();
                }
            });
        }

        if (navGallery != null) {
            navGallery.setOnClickListener(v -> {
                animateTabClick(v);
                if (!activity.getClass().getSimpleName().equals("GalleryActivity")) {
                    Intent intent = new Intent(activity, GalleryActivity.class);
                    activity.startActivity(intent);
                    activity.finish();
                }
            });
        }

        if (navFavorites != null) {
            navFavorites.setOnClickListener(v -> {
                animateTabClick(v);
                if (!activity.getClass().getSimpleName().equals("FavoritesActivity")) {
                    Intent intent = new Intent(activity, FavoritesActivity.class);
                    activity.startActivity(intent);
                    activity.finish();
                }
            });
        }

        if (navMore != null) {
            navMore.setOnClickListener(v -> {
                animateTabClick(v);
                if (!activity.getClass().getSimpleName().equals("MoreActivity")) {
                    Intent intent = new Intent(activity, MoreActivity.class);
                    activity.startActivity(intent);
                    activity.finish();
                }
            });
        }
    }

    public static void setActiveTab(Activity activity, int activeTabIndex) {
        // Reset all tabs first
        resetAllTabs(activity);

        // Set the active tab
        switch (activeTabIndex) {
            case TAB_HOME:
                activateTab(activity, R.id.nav_home, R.id.nav_home_icon,
                           R.id.nav_home_text, R.id.nav_home_indicator, R.color.nav_home_color);
                break;
            case TAB_CATEGORIES:
                activateTab(activity, R.id.nav_categories, R.id.nav_categories_icon,
                           R.id.nav_categories_text, R.id.nav_categories_indicator, R.color.nav_categories_color);
                break;
            case TAB_GALLERY:
                activateTab(activity, R.id.nav_gallery, R.id.nav_gallery_icon,
                           R.id.nav_gallery_text, R.id.nav_gallery_indicator, R.color.nav_gallery_color);
                break;
            case TAB_FAVORITES:
                activateTab(activity, R.id.nav_favorites, R.id.nav_favorites_icon,
                           R.id.nav_favorites_text, R.id.nav_favorites_indicator, R.color.nav_favorites_color);
                break;
            case TAB_MORE:
                activateTab(activity, R.id.nav_more, R.id.nav_more_icon,
                           R.id.nav_more_text, R.id.nav_more_indicator, R.color.nav_more_color);
                break;
        }
    }

    private static void activateTab(Activity activity, int tabId, int iconId, int textId, 
                                   int indicatorId, int colorRes) {
        LinearLayout tab = activity.findViewById(tabId);
        if (tab != null) {
            CardView icon = activity.findViewById(iconId);
            TextView text = activity.findViewById(textId);
            View indicator = activity.findViewById(indicatorId);

            if (icon != null) {
                icon.setCardBackgroundColor(ContextCompat.getColor(activity, colorRes));
                // Add scale animation
                animateIconScale(icon, 1.1f);
            }
            
            if (text != null) {
                text.setTextColor(ContextCompat.getColor(activity, colorRes));
                text.setTypeface(null, Typeface.BOLD);
            }
            
            if (indicator != null) {
                indicator.setVisibility(View.VISIBLE);
                animateIndicator(indicator);
            }
        }
    }

    public static void resetAllTabs(Activity activity) {
        resetTab(activity, R.id.nav_home, R.id.nav_home_icon, R.id.nav_home_text, R.id.nav_home_indicator);
        resetTab(activity, R.id.nav_categories, R.id.nav_categories_icon, R.id.nav_categories_text, R.id.nav_categories_indicator);
        resetTab(activity, R.id.nav_gallery, R.id.nav_gallery_icon, R.id.nav_gallery_text, R.id.nav_gallery_indicator);
        resetTab(activity, R.id.nav_favorites, R.id.nav_favorites_icon, R.id.nav_favorites_text, R.id.nav_favorites_indicator);
        resetTab(activity, R.id.nav_more, R.id.nav_more_icon, R.id.nav_more_text, R.id.nav_more_indicator);
    }

    private static void resetTab(Activity activity, int tabId, int iconId, int textId, int indicatorId) {
        LinearLayout tab = activity.findViewById(tabId);
        if (tab != null) {
            CardView icon = activity.findViewById(iconId);
            TextView text = activity.findViewById(textId);
            View indicator = activity.findViewById(indicatorId);

            if (icon != null) {
                icon.setCardBackgroundColor(ContextCompat.getColor(activity, R.color.text_secondary));
                // Reset scale
                animateIconScale(icon, 1.0f);
            }
            
            if (text != null) {
                text.setTextColor(ContextCompat.getColor(activity, R.color.text_primary));
                text.setTypeface(null, Typeface.NORMAL);
            }
            
            if (indicator != null) {
                indicator.setVisibility(View.GONE);
            }
        }
    }

    private static void animateTabClick(View view) {
        ObjectAnimator scaleX = ObjectAnimator.ofFloat(view, "scaleX", 1.0f, 0.95f, 1.0f);
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(view, "scaleY", 1.0f, 0.95f, 1.0f);
        
        AnimatorSet animatorSet = new AnimatorSet();
        animatorSet.playTogether(scaleX, scaleY);
        animatorSet.setDuration(150);
        animatorSet.start();
    }

    private static void animateIconScale(View view, float scale) {
        ObjectAnimator scaleX = ObjectAnimator.ofFloat(view, "scaleX", scale);
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(view, "scaleY", scale);
        
        AnimatorSet animatorSet = new AnimatorSet();
        animatorSet.playTogether(scaleX, scaleY);
        animatorSet.setDuration(200);
        animatorSet.start();
    }

    private static void animateIndicator(View view) {
        ObjectAnimator alpha = ObjectAnimator.ofFloat(view, "alpha", 0.0f, 1.0f);
        ObjectAnimator scaleX = ObjectAnimator.ofFloat(view, "scaleX", 0.0f, 1.0f);
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(view, "scaleY", 0.0f, 1.0f);
        
        AnimatorSet animatorSet = new AnimatorSet();
        animatorSet.playTogether(alpha, scaleX, scaleY);
        animatorSet.setDuration(300);
        animatorSet.start();
    }
}
