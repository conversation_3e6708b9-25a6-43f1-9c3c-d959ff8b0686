package com.alwan.kids2025;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import androidx.appcompat.widget.Toolbar;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;
import android.widget.LinearLayout;

public class FavoritesActivity extends BaseActivity {

    private CardView emptyFavoritesCard;
    private CardView btnBrowseCategories;
    private RecyclerView favoritesRecyclerView;
    private LinearLayout favoritesActions;
    private CardView btnClearFavorites;
    private CardView btnExportFavorites;
    private FavoritesManager favoritesManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_favorites);

        // Initialize favorites manager
        favoritesManager = new FavoritesManager(this);

        // Setup toolbar
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);

        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
            getSupportActionBar().setTitle("المفضلة");
        }

        // Initialize views
        initViews();

        // Setup listeners
        setupListeners();

        // Load favorites data
        loadFavoritesData();
    }

    private void initViews() {
        emptyFavoritesCard = findViewById(R.id.empty_favorites_card);
        btnBrowseCategories = findViewById(R.id.btn_browse_categories);
        favoritesRecyclerView = findViewById(R.id.favorites_recycler_view);
        favoritesActions = findViewById(R.id.favorites_actions);
        btnClearFavorites = findViewById(R.id.btn_clear_favorites);
        btnExportFavorites = findViewById(R.id.btn_export_favorites);
    }

    private void setupListeners() {
        btnBrowseCategories.setOnClickListener(v -> {
            Intent intent = new Intent(this, Categories.class);
            startActivity(intent);
        });

        btnClearFavorites.setOnClickListener(v -> {
            clearAllFavorites();
        });

        btnExportFavorites.setOnClickListener(v -> {
            exportFavorites();
        });
    }

    private void loadFavoritesData() {
        if (favoritesManager.getFavoritesCount() > 0) {
            showFavoritesContent();
        } else {
            showEmptyState();
        }
    }

    private void clearAllFavorites() {
        favoritesManager.clearAllFavorites();
        showEmptyState();
    }

    private void exportFavorites() {
        // TODO: Implement export functionality
    }

    private void showEmptyState() {
        emptyFavoritesCard.setVisibility(View.VISIBLE);
        favoritesRecyclerView.setVisibility(View.GONE);
        favoritesActions.setVisibility(View.GONE);
    }

    private void showFavoritesContent() {
        emptyFavoritesCard.setVisibility(View.GONE);
        favoritesRecyclerView.setVisibility(View.VISIBLE);
        favoritesActions.setVisibility(View.VISIBLE);
    }

    @Override
    public boolean onSupportNavigateUp() {
        finish();
        return true;
    }

    @Override
    protected boolean useToolbar() {
        return true;
    }

    protected boolean useDrawerToggle() {
        return false;
    }
}
