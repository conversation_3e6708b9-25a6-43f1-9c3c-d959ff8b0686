package com.alwan.kids2025;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import androidx.appcompat.widget.Toolbar;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;
import android.widget.LinearLayout;

public class FavoritesActivity extends BaseLocalizedActivity {

    private CardView emptyFavoritesCard;
    private CardView btnBrowseCategories;
    private RecyclerView favoritesRecyclerView;
    private LinearLayout favoritesActions;
    private CardView btnClearFavorites;
    private CardView btnExportFavorites;
    private FavoritesManager favoritesManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_favorites);

        // Initialize favorites manager
        favoritesManager = new FavoritesManager(this);

        // Setup toolbar
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);

        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
            getSupportActionBar().setTitle("المفضلة");
        }

        // Initialize views
        initViews();

        // Setup listeners
        setupListeners();

        // Load favorites data
        loadFavoritesData();

        // Setup bottom navigation
        BottomNavigationHelper.setupBottomNavigation(this, BottomNavigationHelper.TAB_FAVORITES);
    }

    private void initViews() {
        emptyFavoritesCard = findViewById(R.id.empty_favorites_card);
        btnBrowseCategories = findViewById(R.id.btn_browse_categories);
        favoritesRecyclerView = findViewById(R.id.favorites_recycler_view);
        favoritesActions = findViewById(R.id.favorites_actions);
        btnClearFavorites = findViewById(R.id.btn_clear_favorites);
        btnExportFavorites = findViewById(R.id.btn_export_favorites);
    }

    private void setupListeners() {
        btnBrowseCategories.setOnClickListener(v -> {
            Intent intent = new Intent(this, HomeActivity.class);
            startActivity(intent);
        });

        btnClearFavorites.setOnClickListener(v -> {
            clearAllFavorites();
        });

        btnExportFavorites.setOnClickListener(v -> {
            exportFavorites();
        });
    }

    private void loadFavoritesData() {
        if (favoritesManager.getFavoritesCount() > 0) {
            showFavoritesContent();
        } else {
            showEmptyState();
        }
    }

    private void clearAllFavorites() {
        favoritesManager.clearAllFavorites();
        showEmptyState();
    }

    private void exportFavorites() {
        // Create a simple text list of favorites for sharing
        StringBuilder favoritesText = new StringBuilder();
        favoritesText.append("قائمة المفضلة - تطبيق التلوين\n");
        favoritesText.append("===================\n\n");

        // Add favorite items (this is a basic implementation)
        int favoritesCount = favoritesManager.getFavoritesCount();
        if (favoritesCount > 0) {
            favoritesText.append("عدد الصور المفضلة: ").append(favoritesCount).append("\n");
            favoritesText.append("تم إنشاء هذه القائمة من تطبيق التلوين للأطفال\n\n");
            favoritesText.append("حمّل التطبيق الآن:\n");
            favoritesText.append("https://play.google.com/store/apps/details?id=").append(getPackageName());

            Intent shareIntent = new Intent(Intent.ACTION_SEND);
            shareIntent.setType("text/plain");
            shareIntent.putExtra(Intent.EXTRA_TEXT, favoritesText.toString());
            shareIntent.putExtra(Intent.EXTRA_SUBJECT, "قائمة المفضلة - تطبيق التلوين");
            startActivity(Intent.createChooser(shareIntent, "مشاركة المفضلة"));
        } else {
            // Show message that there are no favorites to export
            android.widget.Toast.makeText(this, "لا توجد صور مفضلة للمشاركة", android.widget.Toast.LENGTH_SHORT).show();
        }
    }

    private void showEmptyState() {
        emptyFavoritesCard.setVisibility(View.VISIBLE);
        favoritesRecyclerView.setVisibility(View.GONE);
        favoritesActions.setVisibility(View.GONE);
    }

    private void showFavoritesContent() {
        emptyFavoritesCard.setVisibility(View.GONE);
        favoritesRecyclerView.setVisibility(View.VISIBLE);
        favoritesActions.setVisibility(View.VISIBLE);
    }

    @Override
    public boolean onSupportNavigateUp() {
        finish();
        return true;
    }

    @Override
    protected boolean useToolbar() {
        return true;
    }

    protected boolean useDrawerToggle() {
        return false;
    }
}
