package com.alwan.kids2025;

import android.content.Context;
import android.content.SharedPreferences;
import java.util.HashSet;
import java.util.Set;

public class FavoritesManager {
    
    private static final String PREFS_NAME = "favorites_prefs";
    private static final String FAVORITES_KEY = "favorite_images";
    
    private SharedPreferences prefs;
    
    public FavoritesManager(Context context) {
        prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
    }
    
    // إضافة صورة للمفضلة
    public void addToFavorites(String imageId) {
        Set<String> favorites = getFavorites();
        favorites.add(imageId);
        saveFavorites(favorites);
    }
    
    // إزالة صورة من المفضلة
    public void removeFromFavorites(String imageId) {
        Set<String> favorites = getFavorites();
        favorites.remove(imageId);
        saveFavorites(favorites);
    }
    
    // التحقق من وجود صورة في المفضلة
    public boolean isFavorite(String imageId) {
        return getFavorites().contains(imageId);
    }
    
    // الحصول على جميع المفضلة
    public Set<String> getFavorites() {
        return new HashSet<>(prefs.getStringSet(FAVORITES_KEY, new HashSet<>()));
    }
    
    // حفظ المفضلة
    private void saveFavorites(Set<String> favorites) {
        prefs.edit().putStringSet(FAVORITES_KEY, favorites).apply();
    }
    
    // مسح جميع المفضلة
    public void clearAllFavorites() {
        prefs.edit().remove(FAVORITES_KEY).apply();
    }
    
    // عدد المفضلة
    public int getFavoritesCount() {
        return getFavorites().size();
    }
}
