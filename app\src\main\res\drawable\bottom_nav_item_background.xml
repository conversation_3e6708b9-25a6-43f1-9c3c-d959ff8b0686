<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#E3F2FD"
                android:endColor="#BBDEFB"
                android:angle="90" />
            <corners android:radius="20dp" />
            <stroke
                android:width="1dp"
                android:color="#90CAF9" />
        </shape>
    </item>
    <item android:state_selected="true">
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#E8F5E8"
                android:endColor="#C8E6C9"
                android:angle="90" />
            <corners android:radius="20dp" />
            <stroke
                android:width="1dp"
                android:color="#81C784" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@android:color/transparent" />
            <corners android:radius="20dp" />
        </shape>
    </item>
</selector>
