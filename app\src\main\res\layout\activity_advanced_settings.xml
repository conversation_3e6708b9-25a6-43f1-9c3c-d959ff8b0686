<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/modern_background">

    <!-- App Bar -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@drawable/modern_gradient_background"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light"
            app:title="الإعدادات المتقدمة" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Settings Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Audio Settings Section -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="إعدادات الصوت"
                android:textColor="@color/modern_primary"
                android:textSize="18sp"
                android:textStyle="bold"
                android:fontFamily="@font/blabeloo"
                android:layout_marginBottom="12dp" />

            <!-- Sound Switch -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@android:color/white">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_volume_up"
                        android:tint="@color/modern_primary"
                        android:layout_marginEnd="16dp" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="تفعيل الأصوات"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            android:fontFamily="@font/blabeloo" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="تشغيل أصوات التطبيق والتأثيرات"
                            android:textColor="@color/text_secondary"
                            android:textSize="12sp"
                            android:fontFamily="@font/blabeloo" />

                    </LinearLayout>

                    <Switch
                        android:id="@+id/sound_switch"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Vibration Switch -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@android:color/white">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_vibration"
                        android:tint="@color/modern_primary"
                        android:layout_marginEnd="16dp" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="تفعيل الاهتزاز"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            android:fontFamily="@font/blabeloo" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="اهتزاز عند اللمس والتفاعل"
                            android:textColor="@color/text_secondary"
                            android:textSize="12sp"
                            android:fontFamily="@font/blabeloo" />

                    </LinearLayout>

                    <Switch
                        android:id="@+id/vibration_switch"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- App Settings Section -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="إعدادات التطبيق"
                android:textColor="@color/modern_primary"
                android:textSize="18sp"
                android:textStyle="bold"
                android:fontFamily="@font/blabeloo"
                android:layout_marginBottom="12dp" />

            <!-- Auto Save Switch -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@android:color/white">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_save"
                        android:tint="@color/nature_color"
                        android:layout_marginEnd="16dp" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="الحفظ التلقائي"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            android:fontFamily="@font/blabeloo" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="حفظ الأعمال تلقائياً أثناء التلوين"
                            android:textColor="@color/text_secondary"
                            android:textSize="12sp"
                            android:fontFamily="@font/blabeloo" />

                    </LinearLayout>

                    <Switch
                        android:id="@+id/auto_save_switch"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Notifications Switch -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@android:color/white">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_notifications"
                        android:tint="@color/cartoons_color"
                        android:layout_marginEnd="16dp" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="الإشعارات"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            android:fontFamily="@font/blabeloo" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="تلقي إشعارات التطبيق والتحديثات"
                            android:textColor="@color/text_secondary"
                            android:textSize="12sp"
                            android:fontFamily="@font/blabeloo" />

                    </LinearLayout>

                    <Switch
                        android:id="@+id/notifications_switch"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Dark Mode Switch -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@android:color/white">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_dark_mode"
                        android:tint="@color/text_secondary"
                        android:layout_marginEnd="16dp" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="الوضع الليلي"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            android:fontFamily="@font/blabeloo" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="تفعيل الوضع الليلي للعيون"
                            android:textColor="@color/text_secondary"
                            android:textSize="12sp"
                            android:fontFamily="@font/blabeloo" />

                    </LinearLayout>

                    <Switch
                        android:id="@+id/dark_mode_switch"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Language and Quality Section -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="إعدادات متقدمة"
                android:textColor="@color/modern_primary"
                android:textSize="18sp"
                android:textStyle="bold"
                android:fontFamily="@font/blabeloo"
                android:layout_marginBottom="12dp" />

            <!-- Language Selection -->
            <androidx.cardview.widget.CardView
                android:id="@+id/language_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:foreground="@drawable/beautiful_ripple_effect"
                android:clickable="true"
                android:focusable="true"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@android:color/white">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_language"
                        android:tint="@color/modern_accent"
                        android:layout_marginEnd="16dp" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="لغة التطبيق"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            android:fontFamily="@font/blabeloo" />

                        <TextView
                            android:id="@+id/language_text"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="العربية"
                            android:textColor="@color/text_secondary"
                            android:textSize="12sp"
                            android:fontFamily="@font/blabeloo" />

                    </LinearLayout>

                    <ImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:src="@drawable/ic_arrow_forward"
                        android:tint="@color/text_secondary" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Image Quality -->
            <androidx.cardview.widget.CardView
                android:id="@+id/quality_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:foreground="@drawable/beautiful_ripple_effect"
                android:clickable="true"
                android:focusable="true"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@android:color/white">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_image_quality"
                        android:tint="@color/flowers_color"
                        android:layout_marginEnd="16dp" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="جودة الصور"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            android:fontFamily="@font/blabeloo" />

                        <TextView
                            android:id="@+id/quality_text"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="عالية"
                            android:textColor="@color/text_secondary"
                            android:textSize="12sp"
                            android:fontFamily="@font/blabeloo" />

                    </LinearLayout>

                    <ImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:src="@drawable/ic_arrow_forward"
                        android:tint="@color/text_secondary" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Cache Management -->
            <androidx.cardview.widget.CardView
                android:id="@+id/cache_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:foreground="@drawable/beautiful_ripple_effect"
                android:clickable="true"
                android:focusable="true"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@android:color/white">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_storage"
                        android:tint="@color/foods_color"
                        android:layout_marginEnd="16dp" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="التخزين المؤقت"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            android:fontFamily="@font/blabeloo" />

                        <TextView
                            android:id="@+id/cache_text"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="2.5 MB"
                            android:textColor="@color/text_secondary"
                            android:textSize="12sp"
                            android:fontFamily="@font/blabeloo" />

                    </LinearLayout>

                    <ImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:src="@drawable/ic_delete"
                        android:tint="@color/text_secondary" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Other Settings Section -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="أخرى"
                android:textColor="@color/modern_primary"
                android:textSize="18sp"
                android:textStyle="bold"
                android:fontFamily="@font/blabeloo"
                android:layout_marginBottom="12dp" />

            <!-- About -->
            <androidx.cardview.widget.CardView
                android:id="@+id/about_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:foreground="@drawable/beautiful_ripple_effect"
                android:clickable="true"
                android:focusable="true"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@android:color/white">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_info"
                        android:tint="@color/modern_primary"
                        android:layout_marginEnd="16dp" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="حول التطبيق"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            android:fontFamily="@font/blabeloo" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="معلومات ومميزات التطبيق"
                            android:textColor="@color/text_secondary"
                            android:textSize="12sp"
                            android:fontFamily="@font/blabeloo" />

                    </LinearLayout>

                    <ImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:src="@drawable/ic_arrow_forward"
                        android:tint="@color/text_secondary" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Reset Settings -->
            <androidx.cardview.widget.CardView
                android:id="@+id/reset_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="80dp"
                android:foreground="@drawable/beautiful_ripple_effect"
                android:clickable="true"
                android:focusable="true"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@android:color/white">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_refresh"
                        android:tint="@color/modern_accent"
                        android:layout_marginEnd="16dp" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="إعادة تعيين الإعدادات"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            android:fontFamily="@font/blabeloo" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="إعادة جميع الإعدادات للقيم الافتراضية"
                            android:textColor="@color/text_secondary"
                            android:textSize="12sp"
                            android:fontFamily="@font/blabeloo" />

                    </LinearLayout>

                    <ImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:src="@drawable/ic_refresh"
                        android:tint="@color/text_secondary" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Bottom Navigation -->
    <include layout="@layout/bottom_navigation_bar" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
