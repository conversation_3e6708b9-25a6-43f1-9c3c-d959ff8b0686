<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/gradient_background"
    android:layoutDirection="locale"
    tools:context=".HomeActivity">

    <!-- Header Section -->
    <LinearLayout
        android:id="@+id/header_section"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="24dp"
        android:gravity="center">

        <!-- App Logo/Icon -->
        <ImageView
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:src="@drawable/ic_app_logo"
            android:layout_marginBottom="16dp"
            android:contentDescription="App Logo" />

        <!-- Welcome Text -->
        <TextView
            android:id="@+id/welcome_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/welcome_message"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            android:fontFamily="@font/blabeloo"
            android:gravity="center"
            android:layout_marginBottom="8dp" />

        <!-- Subtitle -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/choose_category"
            android:textSize="16sp"
            android:textColor="@color/text_secondary"
            android:fontFamily="@font/blabeloo"
            android:gravity="center" />

    </LinearLayout>

    <!-- Categories Grid Section -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="280dp"
        android:fillViewport="true"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Row 1: Flowers & Cartoons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="16dp">

                <!-- Flowers Card -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:orientation="vertical"
                    android:gravity="center">

                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="140dp"
                        app:cardCornerRadius="20dp"
                        app:cardElevation="8dp"
                        app:cardUseCompatPadding="true"
                        app:cardBackgroundColor="@color/flowers_color">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="16dp">

                            <ImageView
                                android:id="@+id/c_1"
                                android:layout_width="80dp"
                                android:layout_height="80dp"
                                android:src="@drawable/ic_flowers_modern"
                                android:layout_marginBottom="8dp"
                                android:contentDescription="Flowers"
                                android:background="?android:attr/selectableItemBackgroundBorderless"
                                android:clickable="true"
                                android:focusable="true" />

                            <TextView
                                android:id="@+id/tv_1"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/flowers"
                                android:textColor="@android:color/white"
                                android:textSize="16sp"
                                android:textStyle="bold"
                                android:fontFamily="@font/blabeloo" />

                        </LinearLayout>

                    </androidx.cardview.widget.CardView>

                </LinearLayout>

                <!-- Cartoons Card -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:orientation="vertical"
                    android:gravity="center">

                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="140dp"
                        app:cardCornerRadius="20dp"
                        app:cardElevation="8dp"
                        app:cardUseCompatPadding="true"
                        app:cardBackgroundColor="@color/cartoons_color">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="16dp">

                            <ImageView
                                android:id="@+id/c_2"
                                android:layout_width="80dp"
                                android:layout_height="80dp"
                                android:src="@drawable/ic_cartoons_modern"
                                android:layout_marginBottom="8dp"
                                android:contentDescription="Cartoons"
                                android:background="?android:attr/selectableItemBackgroundBorderless"
                                android:clickable="true"
                                android:focusable="true" />

                            <TextView
                                android:id="@+id/tv_2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/cartoons"
                                android:textColor="@android:color/white"
                                android:textSize="16sp"
                                android:textStyle="bold"
                                android:fontFamily="@font/blabeloo" />

                        </LinearLayout>

                    </androidx.cardview.widget.CardView>

                </LinearLayout>

            </LinearLayout>

            <!-- Row 2: Animals & Foods -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="16dp">

                <!-- Animals Card -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:orientation="vertical"
                    android:gravity="center">

                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="140dp"
                        app:cardCornerRadius="20dp"
                        app:cardElevation="8dp"
                        app:cardUseCompatPadding="true"
                        app:cardBackgroundColor="@color/animals_color">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="16dp">

                            <ImageView
                                android:id="@+id/c_3"
                                android:layout_width="80dp"
                                android:layout_height="80dp"
                                android:src="@drawable/ic_animals_modern"
                                android:layout_marginBottom="8dp"
                                android:contentDescription="Animals"
                                android:background="?android:attr/selectableItemBackgroundBorderless"
                                android:clickable="true"
                                android:focusable="true" />

                            <TextView
                                android:id="@+id/tv_3"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/animals"
                                android:textColor="@android:color/white"
                                android:textSize="16sp"
                                android:textStyle="bold"
                                android:fontFamily="@font/blabeloo" />

                        </LinearLayout>

                    </androidx.cardview.widget.CardView>

                </LinearLayout>

                <!-- Foods Card -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:orientation="vertical"
                    android:gravity="center">

                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="140dp"
                        app:cardCornerRadius="20dp"
                        app:cardElevation="8dp"
                        app:cardUseCompatPadding="true"
                        app:cardBackgroundColor="@color/foods_color">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="16dp">

                            <ImageView
                                android:id="@+id/c_4"
                                android:layout_width="80dp"
                                android:layout_height="80dp"
                                android:src="@drawable/ic_foods_modern"
                                android:layout_marginBottom="8dp"
                                android:contentDescription="Foods"
                                android:background="?android:attr/selectableItemBackgroundBorderless"
                                android:clickable="true"
                                android:focusable="true" />

                            <TextView
                                android:id="@+id/tv_4"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/foods"
                                android:textColor="@android:color/white"
                                android:textSize="16sp"
                                android:textStyle="bold"
                                android:fontFamily="@font/blabeloo" />

                        </LinearLayout>

                    </androidx.cardview.widget.CardView>

                </LinearLayout>

            </LinearLayout>

            <!-- Row 3: Transport & Nature -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="100dp">

                <!-- Transport Card -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:orientation="vertical"
                    android:gravity="center">

                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="140dp"
                        app:cardCornerRadius="20dp"
                        app:cardElevation="8dp"
                        app:cardUseCompatPadding="true"
                        app:cardBackgroundColor="@color/transport_color">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="16dp">

                            <ImageView
                                android:id="@+id/c_5"
                                android:layout_width="80dp"
                                android:layout_height="80dp"
                                android:src="@drawable/ic_transport_modern"
                                android:layout_marginBottom="8dp"
                                android:contentDescription="Transport"
                                android:background="?android:attr/selectableItemBackgroundBorderless"
                                android:clickable="true"
                                android:focusable="true" />

                            <TextView
                                android:id="@+id/tv_5"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/transport"
                                android:textColor="@android:color/white"
                                android:textSize="16sp"
                                android:textStyle="bold"
                                android:fontFamily="@font/blabeloo" />

                        </LinearLayout>

                    </androidx.cardview.widget.CardView>

                </LinearLayout>

                <!-- Nature Card -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:orientation="vertical"
                    android:gravity="center">

                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="140dp"
                        app:cardCornerRadius="20dp"
                        app:cardElevation="8dp"
                        app:cardUseCompatPadding="true"
                        app:cardBackgroundColor="@color/nature_color">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="16dp">

                            <ImageView
                                android:id="@+id/c_6"
                                android:layout_width="80dp"
                                android:layout_height="80dp"
                                android:src="@drawable/ic_nature_modern"
                                android:layout_marginBottom="8dp"
                                android:contentDescription="Nature"
                                android:background="?android:attr/selectableItemBackgroundBorderless"
                                android:clickable="true"
                                android:focusable="true" />

                            <TextView
                                android:id="@+id/tv_6"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/nature"
                                android:textColor="@android:color/white"
                                android:textSize="16sp"
                                android:textStyle="bold"
                                android:fontFamily="@font/blabeloo" />

                        </LinearLayout>

                    </androidx.cardview.widget.CardView>

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Ad Banner -->
    <com.google.android.gms.ads.AdView
        android:id="@+id/adView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|center_horizontal"
        android:layout_marginBottom="80dp"
        app:adSize="BANNER"
        app:adUnitId="@string/banner_unit_id" />

    <!-- Bottom Navigation -->
    <include layout="@layout/bottom_navigation" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
