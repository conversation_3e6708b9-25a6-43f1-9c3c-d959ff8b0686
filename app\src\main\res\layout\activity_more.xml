<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/modern_background">

    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="80dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Header Section -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="20dp"
                app:cardElevation="8dp"
                app:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/more_header_background"
                    android:orientation="vertical"
                    android:padding="24dp"
                    android:gravity="center">

                    <ImageView
                        android:layout_width="80dp"
                        android:layout_height="80dp"
                        android:layout_marginBottom="16dp"
                        android:src="@drawable/ic_more_modern"
                        android:tint="@android:color/white" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="المزيد من الخيارات"
                        android:textColor="@android:color/white"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        android:fontFamily="@font/blabeloo"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="إعدادات ومعلومات إضافية"
                        android:textColor="@android:color/white"
                        android:textSize="16sp"
                        android:fontFamily="@font/blabeloo"
                        android:alpha="0.9" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Settings Section -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="20dp"
                app:cardElevation="8dp"
                app:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="الإعدادات"
                        android:textColor="@color/modern_primary"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:fontFamily="@font/blabeloo"
                        android:layout_marginBottom="16dp" />

                    <!-- Settings Item -->
                    <LinearLayout
                        android:id="@+id/more_settings"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:background="@drawable/more_item_background"
                        android:clickable="true"
                        android:focusable="true"
                        android:padding="16dp"
                        android:layout_marginBottom="8dp">

                        <androidx.cardview.widget.CardView
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_marginEnd="16dp"
                            app:cardCornerRadius="20dp"
                            app:cardElevation="4dp"
                            app:cardBackgroundColor="@color/modern_primary">

                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:layout_gravity="center"
                                android:src="@drawable/ic_settings_modern"
                                android:tint="@android:color/white" />

                        </androidx.cardview.widget.CardView>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="الإعدادات"
                                android:textColor="@color/text_primary"
                                android:textSize="16sp"
                                android:textStyle="bold"
                                android:fontFamily="@font/blabeloo" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="إعدادات الصوت والعرض"
                                android:textColor="@color/text_secondary"
                                android:textSize="12sp"
                                android:fontFamily="@font/blabeloo" />

                        </LinearLayout>

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_arrow_forward"
                            android:tint="@color/text_secondary" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- App Info Section -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="20dp"
                app:cardElevation="8dp"
                app:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="معلومات التطبيق"
                        android:textColor="@color/cartoons_color"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:fontFamily="@font/blabeloo"
                        android:layout_marginBottom="16dp" />

                    <!-- About Item -->
                    <LinearLayout
                        android:id="@+id/more_about"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:background="@drawable/more_item_background"
                        android:clickable="true"
                        android:focusable="true"
                        android:padding="16dp"
                        android:layout_marginBottom="8dp">

                        <androidx.cardview.widget.CardView
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_marginEnd="16dp"
                            app:cardCornerRadius="20dp"
                            app:cardElevation="4dp"
                            app:cardBackgroundColor="@color/cartoons_color">

                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:layout_gravity="center"
                                android:src="@drawable/ic_info_modern"
                                android:tint="@android:color/white" />

                        </androidx.cardview.widget.CardView>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="حول التطبيق"
                                android:textColor="@color/text_primary"
                                android:textSize="16sp"
                                android:textStyle="bold"
                                android:fontFamily="@font/blabeloo" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="معلومات ومميزات التطبيق"
                                android:textColor="@color/text_secondary"
                                android:textSize="12sp"
                                android:fontFamily="@font/blabeloo" />

                        </LinearLayout>

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_arrow_forward"
                            android:tint="@color/text_secondary" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Share & Support Section -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="20dp"
                app:cardElevation="8dp"
                app:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="المشاركة والدعم"
                        android:textColor="@color/nature_color"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:fontFamily="@font/blabeloo"
                        android:layout_marginBottom="16dp" />

                    <!-- Share Item -->
                    <LinearLayout
                        android:id="@+id/more_share"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:background="@drawable/more_item_background"
                        android:clickable="true"
                        android:focusable="true"
                        android:padding="16dp"
                        android:layout_marginBottom="8dp">

                        <androidx.cardview.widget.CardView
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_marginEnd="16dp"
                            app:cardCornerRadius="20dp"
                            app:cardElevation="4dp"
                            app:cardBackgroundColor="@color/nature_color">

                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:layout_gravity="center"
                                android:src="@drawable/ic_share_modern"
                                android:tint="@android:color/white" />

                        </androidx.cardview.widget.CardView>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="مشاركة التطبيق"
                                android:textColor="@color/text_primary"
                                android:textSize="16sp"
                                android:textStyle="bold"
                                android:fontFamily="@font/blabeloo" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="شارك التطبيق مع الأصدقاء"
                                android:textColor="@color/text_secondary"
                                android:textSize="12sp"
                                android:fontFamily="@font/blabeloo" />

                        </LinearLayout>

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_arrow_forward"
                            android:tint="@color/text_secondary" />

                    </LinearLayout>

                    <!-- Rate Item -->
                    <LinearLayout
                        android:id="@+id/more_rate"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:background="@drawable/more_item_background"
                        android:clickable="true"
                        android:focusable="true"
                        android:padding="16dp"
                        android:layout_marginBottom="8dp">

                        <androidx.cardview.widget.CardView
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_marginEnd="16dp"
                            app:cardCornerRadius="20dp"
                            app:cardElevation="4dp"
                            app:cardBackgroundColor="@color/modern_accent">

                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:layout_gravity="center"
                                android:src="@drawable/ic_star_modern"
                                android:tint="@android:color/white" />

                        </androidx.cardview.widget.CardView>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="تقييم التطبيق"
                                android:textColor="@color/text_primary"
                                android:textSize="16sp"
                                android:textStyle="bold"
                                android:fontFamily="@font/blabeloo" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="قيم التطبيق في المتجر"
                                android:textColor="@color/text_secondary"
                                android:textSize="12sp"
                                android:fontFamily="@font/blabeloo" />

                        </LinearLayout>

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_arrow_forward"
                            android:tint="@color/text_secondary" />

                    </LinearLayout>

                    <!-- Privacy Item -->
                    <LinearLayout
                        android:id="@+id/more_privacy"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:background="@drawable/more_item_background"
                        android:clickable="true"
                        android:focusable="true"
                        android:padding="16dp">

                        <androidx.cardview.widget.CardView
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_marginEnd="16dp"
                            app:cardCornerRadius="20dp"
                            app:cardElevation="4dp"
                            app:cardBackgroundColor="@color/text_secondary">

                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:layout_gravity="center"
                                android:src="@drawable/ic_privacy_modern"
                                android:tint="@android:color/white" />

                        </androidx.cardview.widget.CardView>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="سياسة الخصوصية"
                                android:textColor="@color/text_primary"
                                android:textSize="16sp"
                                android:textStyle="bold"
                                android:fontFamily="@font/blabeloo" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="اطلع على سياسة الخصوصية"
                                android:textColor="@color/text_secondary"
                                android:textSize="12sp"
                                android:fontFamily="@font/blabeloo" />

                        </LinearLayout>

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_arrow_forward"
                            android:tint="@color/text_secondary" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Bottom Navigation (Include) -->
    <include layout="@layout/bottom_navigation_bar" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
