<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/bottom_navigation"
    android:layout_width="match_parent"
    android:layout_height="80dp"
    android:layout_gravity="bottom"
    android:background="@color/card_background"
    android:elevation="8dp"
    android:orientation="horizontal"
    android:paddingTop="8dp"
    android:paddingBottom="8dp">

    <!-- Home Tab -->
    <LinearLayout
        android:id="@+id/nav_home"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center"
        android:orientation="vertical"
        android:background="?android:attr/selectableItemBackgroundBorderless"
        android:clickable="true"
        android:focusable="true">

        <androidx.cardview.widget.CardView
            android:id="@+id/nav_home_icon"
            android:layout_width="32dp"
            android:layout_height="32dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="0dp"
            app:cardBackgroundColor="@color/text_secondary">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_gravity="center"
                android:src="@drawable/ic_home_modern"
                android:tint="@android:color/white" />

        </androidx.cardview.widget.CardView>

        <TextView
            android:id="@+id/nav_home_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="@string/nav_home"
            android:textColor="@color/text_primary"
            android:textSize="10sp"
            android:fontFamily="@font/blabeloo" />

        <View
            android:id="@+id/nav_home_indicator"
            android:layout_width="24dp"
            android:layout_height="2dp"
            android:layout_marginTop="2dp"
            android:background="@color/nav_home_color"
            android:visibility="gone" />

    </LinearLayout>

    <!-- Categories Tab -->
    <LinearLayout
        android:id="@+id/nav_categories"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center"
        android:orientation="vertical"
        android:background="?android:attr/selectableItemBackgroundBorderless"
        android:clickable="true"
        android:focusable="true">

        <androidx.cardview.widget.CardView
            android:id="@+id/nav_categories_icon"
            android:layout_width="32dp"
            android:layout_height="32dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="0dp"
            app:cardBackgroundColor="@color/text_secondary">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_gravity="center"
                android:src="@drawable/ic_categories_modern"
                android:tint="@android:color/white" />

        </androidx.cardview.widget.CardView>

        <TextView
            android:id="@+id/nav_categories_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="@string/nav_categories"
            android:textColor="@color/text_primary"
            android:textSize="10sp"
            android:fontFamily="@font/blabeloo" />

        <View
            android:id="@+id/nav_categories_indicator"
            android:layout_width="24dp"
            android:layout_height="2dp"
            android:layout_marginTop="2dp"
            android:background="@color/nav_categories_color"
            android:visibility="gone" />

    </LinearLayout>

    <!-- Gallery Tab -->
    <LinearLayout
        android:id="@+id/nav_gallery"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center"
        android:orientation="vertical"
        android:background="?android:attr/selectableItemBackgroundBorderless"
        android:clickable="true"
        android:focusable="true">

        <androidx.cardview.widget.CardView
            android:id="@+id/nav_gallery_icon"
            android:layout_width="32dp"
            android:layout_height="32dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="0dp"
            app:cardBackgroundColor="@color/text_secondary">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_gravity="center"
                android:src="@drawable/ic_gallery_modern"
                android:tint="@android:color/white" />

        </androidx.cardview.widget.CardView>

        <TextView
            android:id="@+id/nav_gallery_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="@string/nav_gallery"
            android:textColor="@color/text_primary"
            android:textSize="10sp"
            android:fontFamily="@font/blabeloo" />

        <View
            android:id="@+id/nav_gallery_indicator"
            android:layout_width="24dp"
            android:layout_height="2dp"
            android:layout_marginTop="2dp"
            android:background="@color/nav_gallery_color"
            android:visibility="gone" />

    </LinearLayout>

    <!-- Favorites Tab -->
    <LinearLayout
        android:id="@+id/nav_favorites"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center"
        android:orientation="vertical"
        android:background="?android:attr/selectableItemBackgroundBorderless"
        android:clickable="true"
        android:focusable="true">

        <androidx.cardview.widget.CardView
            android:id="@+id/nav_favorites_icon"
            android:layout_width="32dp"
            android:layout_height="32dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="0dp"
            app:cardBackgroundColor="@color/text_secondary">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_gravity="center"
                android:src="@drawable/ic_favorites_modern"
                android:tint="@android:color/white" />

        </androidx.cardview.widget.CardView>

        <TextView
            android:id="@+id/nav_favorites_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="@string/nav_favorites"
            android:textColor="@color/text_primary"
            android:textSize="10sp"
            android:fontFamily="@font/blabeloo" />

        <View
            android:id="@+id/nav_favorites_indicator"
            android:layout_width="24dp"
            android:layout_height="2dp"
            android:layout_marginTop="2dp"
            android:background="@color/nav_favorites_color"
            android:visibility="gone" />

    </LinearLayout>

    <!-- More Tab -->
    <LinearLayout
        android:id="@+id/nav_more"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center"
        android:orientation="vertical"
        android:background="?android:attr/selectableItemBackgroundBorderless"
        android:clickable="true"
        android:focusable="true">

        <androidx.cardview.widget.CardView
            android:id="@+id/nav_more_icon"
            android:layout_width="32dp"
            android:layout_height="32dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="0dp"
            app:cardBackgroundColor="@color/text_secondary">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_gravity="center"
                android:src="@drawable/ic_more_modern"
                android:tint="@android:color/white" />

        </androidx.cardview.widget.CardView>

        <TextView
            android:id="@+id/nav_more_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="@string/nav_more"
            android:textColor="@color/text_primary"
            android:textSize="10sp"
            android:fontFamily="@font/blabeloo" />

        <View
            android:id="@+id/nav_more_indicator"
            android:layout_width="24dp"
            android:layout_height="2dp"
            android:layout_marginTop="2dp"
            android:background="@color/nav_more_color"
            android:visibility="gone" />

    </LinearLayout>

</LinearLayout>
