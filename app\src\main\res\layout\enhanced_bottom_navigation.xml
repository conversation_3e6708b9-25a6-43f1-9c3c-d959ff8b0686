<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/modern_background">

    <!-- Main Content Container -->
    <FrameLayout
        android:id="@+id/main_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="95dp" />

    <!-- Enhanced Bottom Navigation Bar -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="85dp"
        android:layout_gravity="bottom"
        app:cardElevation="24dp"
        app:cardCornerRadius="25dp"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:layout_marginBottom="8dp"
        app:cardBackgroundColor="@android:color/white">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            android:background="@drawable/bottom_nav_background"
            android:paddingTop="12dp"
            android:paddingBottom="12dp"
            android:paddingStart="8dp"
            android:paddingEnd="8dp">

            <!-- Home Tab -->
            <LinearLayout
                android:id="@+id/nav_home"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center"
                android:background="@drawable/premium_nav_ripple_effect"
                android:clickable="true"
                android:focusable="true"
                android:padding="6dp">

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <androidx.cardview.widget.CardView
                        android:id="@+id/nav_home_icon"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_marginBottom="6dp"
                        app:cardCornerRadius="20dp"
                        app:cardElevation="8dp"
                        app:cardBackgroundColor="@color/nav_home_color">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center"
                            android:src="@drawable/ic_home_modern"
                            android:tint="@android:color/white" />

                    </androidx.cardview.widget.CardView>

                    <!-- Active indicator -->
                    <View
                        android:id="@+id/nav_home_indicator"
                        android:layout_width="6dp"
                        android:layout_height="6dp"
                        android:layout_centerHorizontal="true"
                        android:layout_below="@id/nav_home_icon"
                        android:layout_marginTop="-3dp"
                        android:background="@drawable/nav_active_indicator"
                        android:visibility="gone" />

                </RelativeLayout>

                <TextView
                    android:id="@+id/nav_home_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Home"
                    android:textColor="@color/text_primary"
                    android:textSize="11sp"
                    android:textStyle="bold"
                    android:fontFamily="@font/blabeloo" />

            </LinearLayout>

            <!-- Categories Tab -->
            <LinearLayout
                android:id="@+id/nav_categories"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center"
                android:background="@drawable/premium_nav_ripple_effect"
                android:clickable="true"
                android:focusable="true"
                android:padding="6dp">

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <androidx.cardview.widget.CardView
                        android:id="@+id/nav_categories_icon"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_marginBottom="6dp"
                        app:cardCornerRadius="20dp"
                        app:cardElevation="8dp"
                        app:cardBackgroundColor="@color/nav_categories_color">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center"
                            android:src="@drawable/ic_categories_modern"
                            android:tint="@android:color/white" />

                    </androidx.cardview.widget.CardView>

                    <!-- Active indicator -->
                    <View
                        android:id="@+id/nav_categories_indicator"
                        android:layout_width="6dp"
                        android:layout_height="6dp"
                        android:layout_centerHorizontal="true"
                        android:layout_below="@id/nav_categories_icon"
                        android:layout_marginTop="-3dp"
                        android:background="@drawable/nav_active_indicator"
                        android:visibility="gone" />

                </RelativeLayout>

                <TextView
                    android:id="@+id/nav_categories_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Categories"
                    android:textColor="@color/text_primary"
                    android:textSize="11sp"
                    android:textStyle="bold"
                    android:fontFamily="@font/blabeloo" />

            </LinearLayout>

            <!-- Gallery Tab -->
            <LinearLayout
                android:id="@+id/nav_gallery"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center"
                android:background="@drawable/premium_nav_ripple_effect"
                android:clickable="true"
                android:focusable="true"
                android:padding="6dp">

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <androidx.cardview.widget.CardView
                        android:id="@+id/nav_gallery_icon"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_marginBottom="6dp"
                        app:cardCornerRadius="20dp"
                        app:cardElevation="8dp"
                        app:cardBackgroundColor="@color/nav_gallery_color">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center"
                            android:src="@drawable/ic_gallery_modern"
                            android:tint="@android:color/white" />

                    </androidx.cardview.widget.CardView>

                    <!-- Active indicator -->
                    <View
                        android:id="@+id/nav_gallery_indicator"
                        android:layout_width="6dp"
                        android:layout_height="6dp"
                        android:layout_centerHorizontal="true"
                        android:layout_below="@id/nav_gallery_icon"
                        android:layout_marginTop="-3dp"
                        android:background="@drawable/nav_active_indicator"
                        android:visibility="gone" />

                </RelativeLayout>

                <TextView
                    android:id="@+id/nav_gallery_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Gallery"
                    android:textColor="@color/text_primary"
                    android:textSize="11sp"
                    android:textStyle="bold"
                    android:fontFamily="@font/blabeloo" />

            </LinearLayout>

            <!-- Favorites Tab -->
            <LinearLayout
                android:id="@+id/nav_favorites"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center"
                android:background="@drawable/premium_nav_ripple_effect"
                android:clickable="true"
                android:focusable="true"
                android:padding="6dp">

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <androidx.cardview.widget.CardView
                        android:id="@+id/nav_favorites_icon"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_marginBottom="6dp"
                        app:cardCornerRadius="20dp"
                        app:cardElevation="8dp"
                        app:cardBackgroundColor="@color/nav_favorites_color">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center"
                            android:src="@drawable/ic_favorites_modern"
                            android:tint="@android:color/white" />

                    </androidx.cardview.widget.CardView>

                    <!-- Active indicator -->
                    <View
                        android:id="@+id/nav_favorites_indicator"
                        android:layout_width="6dp"
                        android:layout_height="6dp"
                        android:layout_centerHorizontal="true"
                        android:layout_below="@id/nav_favorites_icon"
                        android:layout_marginTop="-3dp"
                        android:background="@drawable/nav_active_indicator"
                        android:visibility="gone" />

                </RelativeLayout>

                <TextView
                    android:id="@+id/nav_favorites_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Favorites"
                    android:textColor="@color/text_primary"
                    android:textSize="11sp"
                    android:textStyle="bold"
                    android:fontFamily="@font/blabeloo" />

            </LinearLayout>

            <!-- More Tab -->
            <LinearLayout
                android:id="@+id/nav_more"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center"
                android:background="@drawable/premium_nav_ripple_effect"
                android:clickable="true"
                android:focusable="true"
                android:padding="6dp">

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <androidx.cardview.widget.CardView
                        android:id="@+id/nav_more_icon"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_marginBottom="6dp"
                        app:cardCornerRadius="20dp"
                        app:cardElevation="8dp"
                        app:cardBackgroundColor="@color/nav_more_color">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center"
                            android:src="@drawable/ic_more_modern"
                            android:tint="@android:color/white" />

                    </androidx.cardview.widget.CardView>

                    <!-- Active indicator -->
                    <View
                        android:id="@+id/nav_more_indicator"
                        android:layout_width="6dp"
                        android:layout_height="6dp"
                        android:layout_centerHorizontal="true"
                        android:layout_below="@id/nav_more_icon"
                        android:layout_marginTop="-3dp"
                        android:background="@drawable/nav_active_indicator"
                        android:visibility="gone" />

                </RelativeLayout>

                <TextView
                    android:id="@+id/nav_more_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="More"
                    android:textColor="@color/text_primary"
                    android:textSize="11sp"
                    android:textStyle="bold"
                    android:fontFamily="@font/blabeloo" />

            </LinearLayout>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
